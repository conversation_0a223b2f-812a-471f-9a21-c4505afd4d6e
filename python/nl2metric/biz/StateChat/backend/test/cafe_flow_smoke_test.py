from test.common_util import verify_session_in_db, StateMachineAPIClient


async def run_cafe_flow_test():
    """测试咖啡店执照申请流程 - 基础冒烟测试"""
    flow_name = "cafe_license_application"
    print("\n" + "=" * 60)
    print(f"🎯 开始咖啡厅注册流程基础冒烟测试: {flow_name}")
    print("=" * 60)

    client = StateMachineAPIClient()

    try:
        # 1. 健康检查
        print("\n📋 步骤1: 服务健康检查")
        await client.health_check()
        print("   ✅ 服务健康检查通过")

        # 2. 创建会话 (指定流程名)
        print("\n📋 步骤2: 创建会话")
        create_session_result = await client.create_session(
            "cafe_user", flow_name=flow_name
        )
        session_id = create_session_result["session_id"]
        assert create_session_result["flow_name"] == flow_name, f"流程应为 {flow_name}"
        assert create_session_result["current_state"] == "welcome_user"
        print(f"   ✅ 会话创建成功: {session_id}")

        # 3. 数据库验证
        print("\n📋 步骤3: 数据库验证")
        await verify_session_in_db(session_id, flow_name)
        print("   ✅ 数据库验证通过")

        # 4. 测试主要流程路径
        print("\n📋 步骤4: 测试主要流程路径")

        # 4.1 欢迎状态
        await client.send_input("我想申请咖啡店营业执照")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_acra_registration"
        print("   ✅ 欢迎状态 -> ACRA注册检查")

        # 4.2 ACRA已注册路径
        await client.send_input("是的，已经注册了")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_commercial_unit"
        print("   ✅ ACRA注册检查 -> 商业单位检查")

        # 4.3 未租赁商业单位路径
        await client.send_input("还没有租")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "find_commercial_unit"
        print("   ✅ 商业单位检查 -> 寻找商业单位")

        # 4.4 提供商业单位信息
        await client.send_input(
            "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
        )
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_business_services"
        print("   ✅ 寻找商业单位 -> 业务服务检查")

        # 4.5 无特殊服务路径
        await client.send_input("无")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_renovation"
        print("   ✅ 业务服务检查 -> 装修检查")

        # 4.6 无装修路径
        await client.send_input("否")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_signboard"
        print("   ✅ 装修检查 -> 招牌检查")

        # 4.7 无招牌路径
        await client.send_input("否")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_hiring"
        print("   ✅ 招牌检查 -> 员工招聘检查")

        # 4.8 无员工招聘路径
        await client.send_input("否")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "check_food_shop_license"
        print("   ✅ 员工招聘检查 -> 食品店许可证检查")

        # 5. 验证会话状态
        print("\n📋 步骤5: 验证会话状态")
        session_info = await client.get_session_info()
        assert session_info["session_id"] == session_id
        assert session_info["flow_name"] == flow_name
        assert session_info["status"] == "active"
        print("   ✅ 会话状态验证通过")

        # 6. 测试流程统计
        print("\n📋 步骤6: 流程统计")
        print(f"   📊 当前状态: {session_info['current_state']}")
        print(f"   📊 流程进度: 核心路径测试完成")
        print(f"   📊 状态转移次数: {len(session_info.get('history', []))}")

        print(f"\n🎉 咖啡厅注册流程基础冒烟测试完成!")
        print(f"   ✅ 测试的流程: {flow_name}")
        print(f"   ✅ 测试的状态数: 8个")
        print(f"   ✅ 会话ID: {session_id}")

        return True

    except Exception as e:
        print(f"\n❌ 基础冒烟测试失败: {e}")
        if "session_id" in locals():
            print(f"   💡 失败时的会话ID: {session_id}")
        import traceback

        traceback.print_exc()
        return False
