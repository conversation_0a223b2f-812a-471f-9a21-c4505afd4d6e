import asyncio

import httpx


from test.common_util import StateMachineAPIClient, verify_session_in_db
from utils.app_config import settings


async def run_default_flow_test():
    """测试默认流程"""
    expected_flow = settings.app.default_flow_name
    print("\n" + "=" * 50)
    print(f"🎯 开始测试默认流程: {expected_flow}")
    print("=" * 50)

    client = StateMachineAPIClient()

    # 1. 创建会话 (不指定流程名，使用默认)
    create_session_result = await client.create_session("default_user")
    session_id = create_session_result["session_id"]
    assert create_session_result["flow_name"] == expected_flow, (
        f"默认流程应为 {expected_flow}"
    )
    assert create_session_result["current_state"] == "welcome_user"

    # 2. 数据库验证
    await verify_session_in_db(session_id, expected_flow)

    await client.send_input("我想使用你们公司的服务A")  # 使用准确的选项值
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "check_registration"

    # 3. 走完流程
    await client.send_input("否，我没有注册")  # 使用准确的选项值
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "register"

    await client.send_input("个人")  # 使用准确的选项值
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    await client.send_input("姓名：张三，手机号13800138000，邮箱 <EMAIL>")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "main_process"

    await client.send_input("服务A")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "service_a"

    # 4. 文件上传测试
    print("\n📁 开始文件上传测试...")
    test_files = await client.create_test_files()

    try:
        # 上传身份证照片
        print("==" * 30)
        print("上传身份证照片")
        id_card_path, id_card_filename = test_files["id_card"]
        await client.upload_file(id_card_path, id_card_filename)
        await client.send_input("我已经上传上传身份证照片")
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "service_a"
        print("==" * 30)
        # 上传申请表格
        print("上传申请表格")

        application_path, application_filename = test_files["application"]
        await client.upload_file(application_path, application_filename)
        current_state_info = await client.get_current_state()
        assert current_state_info["current_state"] == "service_a"
        print("✅ 所有必需文件已上传完成")

    finally:
        # 清理测试文件
        await client.cleanup_test_files(test_files)

    # 继续流程
    await client.send_input("文件已上传完成，请继续处理")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "process_service_a"

    await client.send_input("继续")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "service_a_completed"

    await client.send_input("完成")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "done"

    session_info = await client.get_session_info()
    assert session_info["status"] == "completed"
    print(f"✅ 默认流程 ({expected_flow}) 测试完成!")


async def main():
    """主测试函数"""
    print("🚀 开始冒烟测试")
    client = StateMachineAPIClient()
    try:
        await client.health_check()
        await run_default_flow_test()
        print("\n🎉🎉🎉 所有测试均已通过! 🎉🎉🎉")

    except httpx.ConnectError:
        print("\n❌ 无法连接到API服务器")
        print(f"   请确保服务器正在 {settings.api.base_url} 上运行: python main.py")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        if client.session_id:
            print(f"   失败时的会话ID: {client.session_id}")
            try:
                await client.get_session_info()
            except Exception as e_info:
                print(f"   获取会话详情失败: {e_info}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
