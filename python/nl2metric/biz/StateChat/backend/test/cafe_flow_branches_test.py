"""
咖啡厅注册流程分支路径测试
测试各种特殊服务和许可证申请的分支路径
"""

import asyncio
import tempfile
import os
from typing import Dict, List

from test.common_util import StateMachineAPIClient, verify_session_in_db
from utils.app_config import settings


class CafeFlowBranchesTest:
    """咖啡厅注册流程分支路径测试"""

    def __init__(self):
        self.client = StateMachineAPIClient()
        self.test_files = {}
        self.session_id = None
        self.flow_name = "cafe_license_application"

    async def setup_test_files(self):
        """设置测试文件"""
        print("\n📁 准备分支测试文件...")

        test_files_data = {
            "id_card": ("身份证.jpg", b"fake_id_card_content"),
            "uen_doc": ("UEN文件.pdf", b"fake_uen_content"),
            "floor_plan": ("平面图.pdf", b"fake_floor_plan_content"),
            "alcohol_menu": ("酒水菜单.pdf", b"fake_alcohol_menu_content"),
            "pet_menu": ("宠物菜单.pdf", b"fake_pet_menu_content"),
            "outdoor_photo": ("户外区域照片.jpg", b"fake_outdoor_photo"),
            "renovation_plan": ("装修计划.pdf", b"fake_renovation_content"),
            "signboard_design": ("招牌设计.jpg", b"fake_signboard_content"),
            "food_hygiene_cert": ("食品卫生证书.pdf", b"fake_hygiene_content"),
        }

        for key, (filename, content) in test_files_data.items():
            file_path = os.path.join(tempfile.gettempdir(), f"test_branch_{filename}")
            with open(file_path, "wb") as f:
                f.write(content)
            self.test_files[key] = (file_path, filename)
            print(f"   ✅ 创建测试文件: {filename}")

    async def cleanup_test_files(self):
        """清理测试文件"""
        print("\n🧹 清理分支测试文件...")
        for key, (file_path, filename) in self.test_files.items():
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"   ✅ 清理: {filename}")
            except Exception as e:
                print(f"   ⚠️  清理失败 {filename}: {e}")

    async def test_acra_not_registered_branch(self):
        """测试ACRA未注册分支"""
        print("\n🌿 测试ACRA未注册分支")

        # 创建新会话
        result = await self.client.create_session("test_acra_branch", self.flow_name)
        session_id = result["session_id"]

        try:
            await self.client.send_input("我想申请咖啡店营业执照")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_acra_registration"

            await self.client.send_input("否")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "register_acra"

            # 测试独资经营路径
            await self.client.send_input("独资经营")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "collect_business_info_and_owner"

            print("   ✅ ACRA未注册分支（独资经营）测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_alcohol_license_branch(self):
        """测试酒类许可证分支"""
        print("\n🍷 测试酒类许可证分支")

        # 创建新会话并快速推进到业务服务检查
        result = await self.client.create_session("test_alcohol_branch", self.flow_name)
        session_id = result["session_id"]

        try:
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_business_services"

            await self.client.send_input("销售酒精饮料")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "liquor_license_flow"

            # 测试酒类许可证申请
            await self.client.send_input("店内饮酒，不晚上10:30后销售")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "liquor_license_submit"

            await self.client.send_input("提交")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_renovation"

            print("   ✅ 酒类许可证分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_pet_cafe_branch(self):
        """测试宠物咖啡馆分支"""
        print("\n🐕 测试宠物咖啡馆分支")

        result = await self.client.create_session("test_pet_branch", self.flow_name)
        session_id = result["session_id"]

        try:
            # 快速推进到业务服务检查
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_business_services"

            await self.client.send_input("允许宠物进入")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "pet_cafe_flow"

            # 测试宠物咖啡馆路径
            await self.client.send_input("为宠物提供食物")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "pet_cafe_license_application"

            await self.client.send_input("宠物菜单和布局图已准备")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_renovation"

            print("   ✅ 宠物咖啡馆分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_outdoor_seating_branch(self):
        """测试户外座位分支"""
        print("\n🪑 测试户外座位分支")

        result = await self.client.create_session("test_outdoor_branch", self.flow_name)
        session_id = result["session_id"]

        try:
            # 快速推进到业务服务检查
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_business_services"

            await self.client.send_input("设置户外座位")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "outdoor_seating_flow"

            # 测试公共土地户外座位
            await self.client.send_input("是")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "ora_permit_application"

            await self.client.send_input("户外座位相关文件已准备")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_renovation"

            print("   ✅ 户外座位分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_renovation_branch(self):
        """测试装修分支"""
        print("\n🔨 测试装修分支")

        result = await self.client.create_session(
            "test_renovation_branch", self.flow_name
        )
        session_id = result["session_id"]

        try:
            # 快速推进到装修检查
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_renovation"

            await self.client.send_input("是")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "fsc_application"

            # 测试装修完成路径
            await self.client.send_input("是")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "fsc_submit_or_download"

            await self.client.send_input("提交")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_signboard"

            print("   ✅ 装修分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_signboard_branch(self):
        """测试招牌分支"""
        print("\n🪧 测试招牌分支")

        result = await self.client.create_session(
            "test_signboard_branch", self.flow_name
        )
        session_id = result["session_id"]

        try:
            # 快速推进到招牌检查
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_signboard"

            await self.client.send_input("是")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "signboard_license_flow"

            await self.client.send_input("招牌设计文件已准备")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "signboard_submit_or_download"

            await self.client.send_input("提交")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_hiring"

            print("   ✅ 招牌分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_hiring_branch(self):
        """测试招聘分支"""
        print("\n👥 测试招聘分支")

        result = await self.client.create_session("test_hiring_branch", self.flow_name)
        session_id = result["session_id"]

        try:
            # 快速推进到招聘检查
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_hiring"

            await self.client.send_input("是")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "cpf_registration_flow"

            await self.client.send_input("CPF注册信息已准备")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "cpf_submit_or_download"

            await self.client.send_input("提交")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_food_shop_license"

            print("   ✅ 招聘分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_gst_branch(self):
        """测试GST注册分支"""
        print("\n💰 测试GST注册分支")

        result = await self.client.create_session("test_gst_branch", self.flow_name)
        session_id = result["session_id"]

        try:
            # 快速推进到GST检查
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("是")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_gst"

            await self.client.send_input("是")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "gst_registration"

            await self.client.send_input("预计年收入120万新元，客流量大")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "bookkeeping_setup"

            print("   ✅ GST注册分支测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def run_all_branch_tests(self):
        """运行所有分支测试"""
        print("\n" + "=" * 60)
        print("🌿 开始咖啡厅注册流程分支路径测试")
        print("=" * 60)

        test_methods = [
            self.test_acra_not_registered_branch,
            self.test_alcohol_license_branch,
            self.test_pet_cafe_branch,
            self.test_outdoor_seating_branch,
            self.test_renovation_branch,
            self.test_signboard_branch,
            self.test_hiring_branch,
            self.test_gst_branch,
        ]

        passed_tests = 0
        total_tests = len(test_methods)

        for test_method in test_methods:
            try:
                await test_method()
                passed_tests += 1
            except Exception as e:
                print(f"\n❌ 测试失败: {test_method.__name__} - {e}")
                # 继续执行其他测试

        print(f"\n📊 分支测试结果: {passed_tests}/{total_tests} 通过")
        return passed_tests == total_tests


async def main():
    """主测试函数"""
    print("🚀 开始咖啡厅注册流程分支路径测试")

    test = CafeFlowBranchesTest()

    try:
        # 健康检查
        await test.client.health_check()

        # 准备测试文件
        await test.setup_test_files()

        # 运行所有分支测试
        success = await test.run_all_branch_tests()

        if success:
            print("\n🎉🎉🎉 所有分支测试均已通过! 🎉🎉🎉")
        else:
            print("\n❌ 部分分支测试失败")
            return False

    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        return False
    finally:
        # 清理测试文件
        await test.cleanup_test_files()

    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
