#!/usr/bin/env python3
"""
配置系统集成测试
专门测试配置系统是否正确集成到各个模块中
"""

import asyncio
import aiohttp
import sys
from pathlib import Path


async def test_server_startup_with_config():
    """测试服务器能否使用配置正确启动"""
    print("🚀 测试服务器配置集成...")

    try:
        from utils.app_config import settings
        from storage.database import init_database

        # 测试配置加载
        print(f"  ✅ 配置加载成功")
        print(f"     - 数据库URL: {settings.get_database_url()}")
        print(f"     - 服务器: {settings.app.host}:{settings.app.port}")
        print(f"     - API基础URL: {settings.api.base_url}")
        print(f"     - 上传目录: {settings.app.uploads_dir}")
        print(f"     - 默认流程: {settings.app.default_flow_name}")
        print(f"     - 运行环境: {settings.app.environment}")

        # 测试数据库初始化
        await init_database()
        print(f"  ✅ 数据库初始化成功")

        return True
    except Exception as e:
        print(f"  ❌ 服务器配置集成测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_api_health_check():
    """测试API健康检查端点"""
    print("🏥 测试API健康检查...")

    try:
        from utils.app_config import settings

        # 构建健康检查URL
        health_url = f"http://{settings.app.host}:{settings.app.port}/health"

        # 注意：这里我们不实际启动服务器，只是验证URL构建正确
        print(f"  ✅ 健康检查URL构建成功: {health_url}")

        # 验证配置值的合理性
        assert settings.app.port > 0 and settings.app.port < 65536, (
            "端口号不在有效范围内"
        )
        assert settings.app.host in ["0.0.0.0", "127.0.0.1", "localhost"], (
            "主机地址不合理"
        )

        print(f"  ✅ 配置值验证通过")
        return True
    except Exception as e:
        print(f"  ❌ API健康检查测试失败: {e}")
        return False


async def test_database_config_usage():
    """测试数据库配置的使用"""
    print("🗄️  测试数据库配置使用...")

    try:
        from storage.database import db_manager
        from utils.app_config import settings

        # 验证数据库管理器使用了正确的配置
        await db_manager.init()

        # 检查数据库URL是否正确使用
        expected_url = settings.get_database_url()
        print(f"  ✅ 数据库URL配置: {expected_url}")

        # 测试数据库配置信息
        db_config = settings.get_database_config()
        print(f"  ✅ 数据库类型: {db_config.type}")
        print(f"  ✅ 数据库文件/名称: {db_config.database}")
        print(f"  ✅ Echo模式: {db_config.echo}")

        # 测试数据库连接
        session = await db_manager.get_session()
        await session.close()
        print(f"  ✅ 数据库连接测试成功")

        await db_manager.close()
        return True
    except Exception as e:
        print(f"  ❌ 数据库配置使用测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_flow_manager_config():
    """测试流程管理器配置使用"""
    print("🔄 测试流程管理器配置...")

    try:
        from core.flow_manager import flow_manager
        from utils.app_config import settings

        # 验证默认流程名配置
        expected_flow = settings.app.default_flow_name
        actual_flow = flow_manager.default_flow_name

        assert actual_flow == expected_flow, (
            f"默认流程名不匹配: 期望 {expected_flow}, 实际 {actual_flow}"
        )

        print(f"  ✅ 默认流程名配置正确: {actual_flow}")

        # 测试流程加载
        flow_config = flow_manager.get_flow_by_name(actual_flow)
        assert flow_config is not None, "默认流程配置加载失败"

        print(f"  ✅ 默认流程配置加载成功")
        return True
    except Exception as e:
        print(f"  ❌ 流程管理器配置测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_file_upload_config():
    """测试文件上传配置"""
    print("📁 测试文件上传配置...")

    try:
        from utils.app_config import settings

        # 验证上传目录配置
        uploads_dir = settings.app.uploads_dir
        print(f"  ✅ 上传目录配置: {uploads_dir}")

        # 验证目录路径的合理性
        assert uploads_dir, "上传目录不能为空"
        assert not uploads_dir.startswith("/"), "上传目录应该是相对路径"

        print(f"  ✅ 上传目录配置验证通过")
        return True
    except Exception as e:
        print(f"  ❌ 文件上传配置测试失败: {e}")
        return False


async def test_environment_variable_override():
    """测试环境变量覆盖功能"""
    print("🌍 测试环境变量覆盖...")

    try:
        import os
        import importlib

        # 设置测试环境变量
        os.environ["PORT"] = "8888"
        os.environ["HOST"] = "127.0.0.1"

        # 重新加载配置模块
        import utils.app_config

        importlib.reload(utils.app_config)

        from utils.app_config import settings

        # 验证环境变量覆盖
        assert settings.app.port == 8888, f"端口环境变量覆盖失败: {settings.app.port}"
        assert settings.app.host == "127.0.0.1", (
            f"主机环境变量覆盖失败: {settings.app.host}"
        )

        print(f"  ✅ 环境变量覆盖成功: {settings.app.host}:{settings.app.port}")

        # 清理环境变量
        del os.environ["PORT"]
        del os.environ["HOST"]

        return True
    except Exception as e:
        print(f"  ❌ 环境变量覆盖测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始配置系统集成测试\n")

    tests = [
        test_server_startup_with_config,
        test_api_health_check,
        test_database_config_usage,
        test_flow_manager_config,
        test_file_upload_config,
        test_environment_variable_override,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if await test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"  ❌ 测试执行异常: {e}")
            print()

    print(f"📊 集成测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有配置系统集成测试通过！")
        print("✅ 配置系统已成功集成到应用的各个模块中")
        return 0
    else:
        print("❌ 部分集成测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
