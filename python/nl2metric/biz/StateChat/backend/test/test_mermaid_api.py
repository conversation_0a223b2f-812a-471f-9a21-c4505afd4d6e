"""
测试Mermaid图表生成API
"""

import asyncio
import json
import aiohttp
from pathlib import Path


async def test_mermaid_api():
    """测试Mermaid图表生成API"""
    base_url = "http://localhost:5432"

    async with aiohttp.ClientSession() as session:
        print("🧪 测试Mermaid图表生成API")
        print("=" * 60)

        # 测试1: 获取默认流程的流程图
        print("\n📊 测试1: 生成默认流程的流程图")
        try:
            async with session.post(
                f"{base_url}/api/flows/mermaid",
                json={
                    "diagram_type": "flowchart",
                    "include_details": True,
                    "style_theme": "colorful",
                },
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 成功生成图表")
                    print(f"   流程名称: {result['flow_name']}")
                    print(f"   图表类型: {result['diagram_type']}")
                    print(f"   统计信息: {result['statistics']}")
                    print(f"   生成时间: {result['generated_at']}")

                    # 保存Mermaid代码到文件
                    output_file = Path("test_output_default_flowchart.mmd")
                    output_file.write_text(result["mermaid_code"], encoding="utf-8")
                    print(f"   📁 Mermaid代码已保存到: {output_file}")
                else:
                    print(f"❌ 请求失败: {resp.status}")
                    print(await resp.text())
        except Exception as e:
            print(f"❌ 测试失败: {e}")

        # 测试2: 生成指定流程的状态图
        print("\n📊 测试2: 生成咖啡店流程的状态图")
        try:
            async with session.post(
                f"{base_url}/api/flows/mermaid",
                json={
                    "flow_name": "cafe_license_application",
                    "diagram_type": "stateDiagram",
                    "include_details": False,
                    "style_theme": "minimal",
                },
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 成功生成状态图")
                    print(f"   流程名称: {result['flow_name']}")
                    print(f"   图表类型: {result['diagram_type']}")
                    print(f"   统计信息: {result['statistics']}")

                    # 保存Mermaid代码到文件
                    output_file = Path("test_output_cafe_statediagram.mmd")
                    output_file.write_text(result["mermaid_code"], encoding="utf-8")
                    print(f"   📁 Mermaid代码已保存到: {output_file}")
                else:
                    print(f"❌ 请求失败: {resp.status}")
                    print(await resp.text())
        except Exception as e:
            print(f"❌ 测试失败: {e}")

        # 测试3: 使用GET方式获取流程图
        print("\n📊 测试3: 使用GET方式获取简化流程图")
        try:
            async with session.get(
                f"{base_url}/api/flows/simple_test_flow/mermaid",
                params={
                    "diagram_type": "flowchart",
                    "include_details": "false",
                    "style_theme": "default",
                },
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 成功生成简化图表")
                    print(f"   流程名称: {result['flow_name']}")
                    print(f"   图表类型: {result['diagram_type']}")
                    print(f"   统计信息: {result['statistics']}")

                    # 保存Mermaid代码到文件
                    output_file = Path("test_output_simple_minimal.mmd")
                    output_file.write_text(result["mermaid_code"], encoding="utf-8")
                    print(f"   📁 Mermaid代码已保存到: {output_file}")
                else:
                    print(f"❌ 请求失败: {resp.status}")
                    print(await resp.text())
        except Exception as e:
            print(f"❌ 测试失败: {e}")

        # 测试4: 测试错误处理
        print("\n📊 测试4: 测试错误处理 - 不存在的流程")
        try:
            async with session.post(
                f"{base_url}/api/flows/mermaid",
                json={"flow_name": "non_existent_flow", "diagram_type": "flowchart"},
            ) as resp:
                if resp.status == 404:
                    print(f"✅ 正确处理了不存在的流程错误")
                    error_detail = await resp.json()
                    print(f"   错误信息: {error_detail.get('detail', 'Unknown error')}")
                else:
                    print(f"❌ 未正确处理错误: {resp.status}")
                    print(await resp.text())
        except Exception as e:
            print(f"❌ 测试失败: {e}")

        # 测试5: 获取流程列表
        print("\n📊 测试5: 获取可用流程列表")
        try:
            async with session.get(f"{base_url}/api/flows") as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 成功获取流程列表")
                    print(f"   可用流程数量: {len(result['flows'])}")
                    for flow in result["flows"]:
                        print(f"   - {flow['flow_name']}: {flow['description']}")
                        print(
                            f"     状态数量: {flow['total_states']}, 版本: {flow['version']}"
                        )
                else:
                    print(f"❌ 请求失败: {resp.status}")
                    print(await resp.text())
        except Exception as e:
            print(f"❌ 测试失败: {e}")

        print("\n" + "=" * 60)
        print("🎉 测试完成！")
        print("📁 生成的Mermaid文件:")
        for file in Path(".").glob("test_output_*.mmd"):
            print(f"   - {file}")


def print_mermaid_usage():
    """打印Mermaid使用说明"""
    print("\n📖 Mermaid图表使用说明:")
    print("=" * 60)
    print("1. 在线查看: 访问 https://mermaid.live/")
    print("2. 复制生成的.mmd文件内容到在线编辑器")
    print("3. 或者使用Mermaid CLI工具:")
    print("   npm install -g @mermaid-js/mermaid-cli")
    print("   mmdc -i input.mmd -o output.png")
    print("4. VS Code插件: Mermaid Preview")
    print("5. 支持的图表类型:")
    print("   - flowchart: 流程图 (推荐)")
    print("   - stateDiagram: 状态图")
    print("6. 支持的样式主题:")
    print("   - default: 默认样式")
    print("   - colorful: 彩色主题")
    print("   - minimal: 简约主题")


if __name__ == "__main__":
    asyncio.run(test_mermaid_api())
    print_mermaid_usage()
