#!/usr/bin/env python3
"""
配置系统验证脚本
验证所有配置功能是否正常工作
"""

import os
import sys
import asyncio
from pathlib import Path

from test.common_util import StateMachineAPIClient


def test_basic_config_loading():
    """测试基本配置加载"""
    print("🔧 测试基本配置加载...")
    try:
        from utils.app_config import settings

        # 使用新的数据库配置方式
        db_url = settings.get_database_url()
        assert db_url, "数据库URL不能为空"
        assert settings.app.uploads_dir, "上传目录不能为空"
        assert settings.app.default_flow_name, "默认流程名不能为空"
        assert settings.app.host, "主机地址不能为空"
        assert settings.app.port > 0, "端口必须大于0"
        assert settings.api.base_url, "API基础URL不能为空"

        print(f"  ✅ 数据库URL: {db_url}")
        print(f"  ✅ 上传目录: {settings.app.uploads_dir}")
        print(f"  ✅ 默认流程: {settings.app.default_flow_name}")
        print(f"  ✅ 服务器: {settings.app.host}:{settings.app.port}")
        print(f"  ✅ API基础URL: {settings.api.base_url}")
        print("✅ 基本配置加载测试通过\n")
        return True
    except Exception as e:
        print(f"❌ 基本配置加载测试失败: {e}")
        return False


def test_environment_variables():
    """测试环境变量覆盖"""
    print("🌍 测试环境变量覆盖...")
    try:
        # 设置环境变量
        os.environ["PORT"] = "9999"
        os.environ["HOST"] = "127.0.0.1"
        os.environ["LOG_LEVEL"] = "DEBUG"

        # 重新导入配置（在实际应用中，这需要重启应用）
        import importlib
        import utils.app_config

        importlib.reload(utils.app_config)

        from utils.app_config import settings

        assert settings.app.port == 9999, f"端口应为9999，实际为{settings.app.port}"
        assert settings.app.host == "127.0.0.1", (
            f"主机应为127.0.0.1，实际为{settings.app.host}"
        )
        assert settings.app.log_level == "DEBUG", (
            f"日志级别应为DEBUG，实际为{settings.app.log_level}"
        )

        print(f"  ✅ 端口覆盖: {settings.app.port}")
        print(f"  ✅ 主机覆盖: {settings.app.host}")
        print(f"  ✅ 日志级别覆盖: {settings.app.log_level}")
        print("✅ 环境变量覆盖测试通过\n")

        # 清理环境变量
        del os.environ["PORT"]
        del os.environ["HOST"]
        del os.environ["LOG_LEVEL"]

        return True
    except Exception as e:
        print(f"❌ 环境变量覆盖测试失败: {e}")
        return False


def test_database_url_construction():
    """测试数据库URL构建"""
    print("🗄️  测试数据库URL构建...")
    try:
        from utils.app_config import settings, DatabaseConfig

        # 测试不同数据库类型的URL构建
        test_configs = [
            {
                "name": "SQLite",
                "config": DatabaseConfig(type="sqlite", database="data/test.db"),
                "expected_pattern": "sqlite+aiosqlite:///",
            },
            {
                "name": "MySQL",
                "config": DatabaseConfig(
                    type="mysql",
                    host="localhost",
                    port=3306,
                    database="testdb",
                    user="testuser",
                    password="testpass",
                ),
                "expected_pattern": "mysql+aiomysql://testuser:testpass@localhost:3306/testdb",
            },
        ]

        for test_case in test_configs:
            url = test_case["config"].get_database_url()
            assert test_case["expected_pattern"] in url, (
                f"{test_case['name']} URL构建错误: {url}"
            )
            print(f"  ✅ {test_case['name']} URL构建: {url}")

        # 测试当前配置的数据库URL
        current_url = settings.get_database_url()
        assert current_url, "当前数据库URL不能为空"
        print(f"  ✅ 当前数据库URL: {current_url}")

        print("✅ 数据库URL构建测试通过\n")
        return True
    except Exception as e:
        print(f"❌ 数据库URL构建测试失败: {e}")
        return False


def test_module_integration():
    """测试模块集成"""
    print("🔗 测试模块集成...")
    try:
        # 重新加载配置以确保使用默认值
        import importlib
        import utils.app_config

        importlib.reload(utils.app_config)

        from storage.database import db_manager
        from core.flow_manager import flow_manager

        # 测试流程管理器
        assert flow_manager.default_flow_name, "默认流程名不能为空"
        print(f"  ✅ 流程管理器默认流程: {flow_manager.default_flow_name}")

        # 测试API客户端
        client = StateMachineAPIClient()
        assert client.base_url, "API客户端基础URL不能为空"
        print(f"  ✅ API客户端基础URL: {client.base_url}")

        print("✅ 模块集成测试通过\n")
        return True
    except Exception as e:
        print(f"❌ 模块集成测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_database_initialization():
    """测试数据库初始化"""
    print("💾 测试数据库初始化...")
    try:
        from storage.database import init_database, cleanup_database

        await init_database()
        print("  ✅ 数据库初始化成功")

        await cleanup_database()
        print("  ✅ 数据库清理成功")

        print("✅ 数据库初始化测试通过\n")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始配置系统验证测试\n")

    tests = [
        test_basic_config_loading,
        test_environment_variables,
        test_database_url_construction,
        test_module_integration,
    ]

    async_tests = [
        test_database_initialization,
    ]

    passed = 0
    total = len(tests) + len(async_tests)

    # 运行同步测试
    for test in tests:
        if test():
            passed += 1

    # 运行异步测试
    for test in async_tests:
        if await test():
            passed += 1

    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有配置系统测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
