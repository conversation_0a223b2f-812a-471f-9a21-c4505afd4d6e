"""
测试智能追问机制
"""

import asyncio

from test.common_util import StateMachineAPIClient, verify_session_in_db
from utils.app_config import settings


async def test_partial_info_with_followup():
    """测试部分信息输入后的智能追问机制"""
    expected_flow = settings.app.default_flow_name
    print("\n" + "=" * 50)
    print(f"🎯 测试智能追问机制: {expected_flow}")
    print("=" * 50)

    client = StateMachineAPIClient()

    # 1. 创建会话
    create_session_result = await client.create_session("test_user")
    session_id = create_session_result["session_id"]
    assert create_session_result["flow_name"] == expected_flow
    assert create_session_result["current_state"] == "welcome_user"

    # 2. 数据库验证
    await verify_session_in_db(session_id, expected_flow)

    # 3. 进入个人信息收集状态
    await client.send_input("我想使用你们公司的服务A")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "check_registration"

    await client.send_input("否，我没有注册")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "register"

    await client.send_input("个人")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    # 4. 测试部分信息输入（只有姓名和手机号，缺少邮箱）
    print("\n📝 测试场景：只提供姓名和手机号")
    response_text = await client.send_input("姓名：张三，手机号13800138000")

    # 验证响应内容包含追问消息
    print(f"📝 响应内容: {response_text}")
    assert "邮箱" in response_text or "email" in response_text
    assert "补充" in response_text or "缺少" in response_text or "缺失" in response_text

    # 验证状态没有转移（仍然在收集个人信息状态）
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    # 5. 补充缺失的邮箱信息
    print("\n📝 补充邮箱信息")
    response_text = await client.send_input("邮箱：<EMAIL>")

    # 验证现在应该成功转移到下一状态
    print(f"📝 响应内容: {response_text}")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "main_process"

    print("✅ 智能追问机制测试完成！")


async def test_no_info_provided():
    """测试完全不提供信息的情况"""
    expected_flow = settings.app.default_flow_name
    print("\n" + "=" * 50)
    print(f"🎯 测试完全不提供信息: {expected_flow}")
    print("=" * 50)

    client = StateMachineAPIClient()

    # 1. 创建会话并进入个人信息收集状态
    create_session_result = await client.create_session("test_user2")
    session_id = create_session_result["session_id"]

    await client.send_input("我想使用你们公司的服务A")
    await client.send_input("否，我没有注册")
    await client.send_input("个人")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    # 2. 测试完全不提供信息
    print("\n📝 测试场景：不提供任何信息")
    response_text = await client.send_input("我不知道")

    # 验证响应包含追问消息
    print(f"📝 响应内容: {response_text}")
    assert "姓名" in response_text or "name" in response_text
    assert "手机号" in response_text or "phone" in response_text
    assert "邮箱" in response_text or "email" in response_text

    # 验证状态没有转移
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    print("✅ 完全不提供信息测试完成！")


async def test_progressive_info_collection():
    """测试渐进式信息收集"""
    expected_flow = settings.app.default_flow_name
    print("\n" + "=" * 50)
    print(f"🎯 测试渐进式信息收集: {expected_flow}")
    print("=" * 50)

    client = StateMachineAPIClient()

    # 1. 创建会话并进入个人信息收集状态
    create_session_result = await client.create_session("test_user3")
    session_id = create_session_result["session_id"]

    await client.send_input("我想使用你们公司的服务A")
    await client.send_input("否，我没有注册")
    await client.send_input("个人")
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    # 2. 逐步提供信息
    print("\n📝 第一步：只提供姓名")
    response_text = await client.send_input("我叫张三")
    print(f"📝 响应内容: {response_text}")
    assert "手机号" in response_text or "邮箱" in response_text

    # 验证状态没有转移
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    print("\n📝 第二步：提供手机号")
    response_text = await client.send_input("我的手机号是13800138000")
    print(f"📝 响应内容: {response_text}")
    assert "邮箱" in response_text or "email" in response_text

    # 验证状态没有转移
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "collect_personal_info"

    print("\n📝 第三步：提供邮箱")
    response_text = await client.send_input("邮箱是********************")
    print(f"📝 响应内容: {response_text}")

    # 验证现在应该成功转移到下一状态
    current_state_info = await client.get_current_state()
    assert current_state_info["current_state"] == "main_process"

    print("✅ 渐进式信息收集测试完成！")


async def main():
    """主测试函数"""
    print("🚀 开始智能追问机制测试")
    client = StateMachineAPIClient()
    try:
        await client.health_check()

        # 运行所有测试
        await test_partial_info_with_followup()
        await test_no_info_provided()
        await test_progressive_info_collection()

        print("\n🎉🎉🎉 所有智能追问机制测试均已通过! 🎉🎉🎉")

    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
