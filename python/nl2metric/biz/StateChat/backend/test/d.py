import concurrent.futures
import random

from langchain_core.runnables import RunnableLambda, RunnableParallel


def random_string():
    return "".join(random.choice("abcdefghijklmnopqrstuvwxyz") for _ in range(10))
chain = RunnableParallel(
    {
        "intent": RunnableLambda(lambda x: {"name": "doc", "arguments": {"query"}}),
        "extract_params": RunnableLambda(
            lambda x: {"text": "", "where": random_string(), "need_text": False},
        ),
    }
) | RunnableLambda(lambda f: {**f["intent"], **f["extract_params"]})
# 构造模拟输入，intent 和 extract_params 均返回正常值
INPUT = {
    "intent": {
        "name": "doc",
        "arguments": {"query": "泰康养老2024年有哪些发文？"}
    },
    "extract_params": {
        "text": "",
        "where": "institution like '%泰康养老股份有限公司%' and year = '2024'",
        "need_text": False,
    }
}


def run_chain_once():
    """
    调用 LangChain 的合并 chain，返回 where 字段
    """
    result = chain.invoke(INPUT)
    return result.get("where", None)


def reproduce_missing_where():
    """
    多线程、多次循环调用，期望至少一次捕获到 where 为空的异常情况。
    若始终未捕获，则认为 race 条件未复现。
    """
    found_empty = False
    iterations = 10000
    workers = 10

    for i in range(iterations):
        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:
            # 同时启动多次调用
            futures = [executor.submit(run_chain_once) for _ in range(workers)]
            for future in concurrent.futures.as_completed(futures):
                where = future.result()
                if where == "" or where is None:
                    found_empty = True
                    break
                else:
                    print(f"第 {i} 次迭代，where 字段正常返回：{where}")
        if found_empty:
            break

    assert found_empty, (
        f"未能在 {iterations} 次迭代的多线程测试中复现 where 丢失问题，"
        "请确认 race 条件或调整测试参数。"
    )


if __name__ == '__main__':
    reproduce_missing_where()
