#!/usr/bin/env python3
"""
测试新进度计算的集成效果
"""

import asyncio
import sys
from pathlib import Path


from utils.config_loader import ConfigLoader
from core.state_machine import StateMachine
from storage.database import db_manager
from models.state import UserInput, InputType


async def test_progress_in_session():
    """测试在实际会话中的进度计算"""
    print("🔄 测试实际会话中的进度计算")
    print("=" * 60)

    # 初始化数据库
    await db_manager.init()

    try:
        # 加载流程配置
        config_loader = ConfigLoader()
        simple_flow = config_loader.load_flow_config(
            "config/flow_config/simple_test_flow.yaml"
        )

        db_session = await db_manager.get_session()
        try:
            state_machine = StateMachine(simple_flow, db_session)

            # 创建新会话
            session = await state_machine.create_session("test_user_123", "")
            session_id = session.session_id

            print(f"📝 创建会话: {session_id}")
            print(f"初始状态: {session.current_state}")

            # 模拟用户交互流程
            test_inputs = [
                ("欢迎信息", "text", "你好"),
                ("注册检查", "choice", "否"),  # 选择未注册
                ("注册类型", "choice", "个人"),  # 选择个人注册
                ("个人信息", "text", "张三，13800138000，<EMAIL>"),
                ("主流程", "choice", "服务A"),  # 选择服务A
                ("服务A", "text", "完成"),
                ("完成", "text", "完成"),
            ]

            print("\n📊 进度变化过程:")
            print("步骤 -> 状态 -> 深度 -> 进度% -> 说明")
            print("-" * 70)

            for i, (step_name, input_type, input_value) in enumerate(test_inputs):
                # 获取当前状态
                current_session = await state_machine.get_session(session_id)
                current_state = current_session.current_state

                # 计算当前进度
                depth = state_machine._calculate_state_depth(current_state)
                progress = state_machine.get_progress(current_state)

                print(
                    f"{i + 1:2d}   -> {current_state:20} -> {depth:2d} -> {progress:5.1f}% -> {step_name}"
                )

                # 处理用户输入
                if i < len(test_inputs) - 1:  # 不处理最后一个输入，只显示最终状态
                    user_input = UserInput(
                        input_type=InputType(input_type), value=input_value
                    )

                    try:
                        response = await state_machine.process_user_input(
                            session_id, user_input
                        )
                        if not response.success:
                            print(f"    ❌ 处理失败: {response}")
                            break
                    except Exception as e:
                        print(f"    ❌ 处理异常: {e}")
                        break

            # 显示最终状态
            final_session = await state_machine.get_session(session_id)
            final_state = final_session.current_state
            final_depth = state_machine._calculate_state_depth(final_state)
            final_progress = state_machine.get_progress(final_state)

            print(
                f"🏁   -> {final_state:20} -> {final_depth:2d} -> {final_progress:5.1f}% -> 最终状态"
            )

            # 获取会话摘要
            summary = await state_machine.get_session_summary(session_id)
            print(f"\n📋 会话摘要:")
            print(f"   用户ID: {summary.user_id}")
            print(f"   流程名: {summary.flow_name}")
            print(f"   当前状态: {summary.current_state}")
            print(f"   状态: {summary.status}")
            print(f"   进度: {summary.progress_percentage:.1f}%")

        finally:
            await db_session.close()

    finally:
        await db_manager.close()


async def test_different_paths():
    """测试不同路径的进度计算"""
    print("\n🛤️  测试不同路径的进度计算")
    print("=" * 60)

    await db_manager.init()

    try:
        config_loader = ConfigLoader()
        simple_flow = config_loader.load_flow_config(
            "config/flow_config/simple_test_flow.yaml"
        )

        db_session = await db_manager.get_session()
        try:
            state_machine = StateMachine(simple_flow, db_session)

            # 测试路径1: 已注册用户 -> 直接到主流程
            print("\n📍 路径1: 已注册用户")
            path1_states = [
                "welcome_user",
                "check_registration",
                "main_process",
                "service_b",
                "done",
            ]

            for state in path1_states:
                depth = state_machine._calculate_state_depth(state)
                progress = state_machine.get_progress(state)
                print(f"   {state:20} -> 深度{depth:2d} -> 进度{progress:5.1f}%")

            # 测试路径2: 未注册个人用户
            print("\n📍 路径2: 未注册个人用户")
            path2_states = [
                "welcome_user",
                "check_registration",
                "register",
                "collect_personal_info",
                "main_process",
                "service_a",
                "done",
            ]

            for state in path2_states:
                depth = state_machine._calculate_state_depth(state)
                progress = state_machine.get_progress(state)
                print(f"   {state:20} -> 深度{depth:2d} -> 进度{progress:5.1f}%")

            # 测试路径3: 未注册企业用户
            print("\n📍 路径3: 未注册企业用户")
            path3_states = [
                "welcome_user",
                "check_registration",
                "register",
                "collect_business_info",
                "main_process",
                "service_c",
                "done",
            ]

            for state in path3_states:
                depth = state_machine._calculate_state_depth(state)
                progress = state_machine.get_progress(state)
                print(f"   {state:20} -> 深度{depth:2d} -> 进度{progress:5.1f}%")

            print("\n💡 观察:")
            print("   - 不同路径的相同深度状态有相似的进度值")
            print("   - 最终状态都是100%")
            print("   - 进度反映了用户在流程中的实际位置")

        finally:
            await db_session.close()

    finally:
        await db_manager.close()


async def main():
    """主测试函数"""
    print("🧪 进度计算集成测试")
    print("=" * 60)

    # 测试实际会话中的进度
    await test_progress_in_session()

    # 测试不同路径的进度
    await test_different_paths()

    print("\n✅ 集成测试完成!")
    print("\n🎯 新进度计算的实际效果:")
    print("   1. 进度更准确反映用户在流程中的位置")
    print("   2. 不同路径的进度计算一致性好")
    print("   3. 最终状态始终为100%")
    print("   4. 进度变化更符合用户预期")


if __name__ == "__main__":
    asyncio.run(main())
