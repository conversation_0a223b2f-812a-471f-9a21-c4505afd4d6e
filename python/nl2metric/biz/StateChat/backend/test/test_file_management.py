"""
测试文件管理功能
"""

import asyncio
import httpx
import os
from pathlib import Path

# 服务器地址
BASE_URL = "http://localhost:5432"


async def test_file_management():
    """测试文件管理功能"""
    async with httpx.AsyncClient() as client:
        # 1. 创建会话
        print("1. 创建会话...")
        response = await client.post(
            f"{BASE_URL}/sessions",
            json={"user_id": "test_user", "flow_name": "simple_test_flow"},
        )

        if response.status_code != 200:
            print(f"创建会话失败: {response.status_code} - {response.text}")
            return

        session_data = response.json()
        session_id = session_data["session_id"]
        print(f"会话创建成功: {session_id}")

        # 2. 上传测试文件
        print("\n2. 上传测试文件...")

        # 创建一个测试文件
        test_file_path = "test_upload.txt"
        with open(test_file_path, "w") as f:
            f.write("这是一个测试文件\n用于测试文件上传和下载功能")

        # 上传文件
        with open(test_file_path, "rb") as f:
            files = {"file": ("test_upload.txt", f, "text/plain")}
            response = await client.post(
                f"{BASE_URL}/sessions/{session_id}/files", files=files
            )

        if response.status_code != 200:
            print(f"文件上传失败: {response.status_code} - {response.text}")
            return

        upload_result = response.json()
        print(f"文件上传成功: {upload_result['filename']}")

        # 3. 获取文件列表
        print("\n3. 获取文件列表...")
        response = await client.get(f"{BASE_URL}/sessions/{session_id}/files")

        if response.status_code != 200:
            print(f"获取文件列表失败: {response.status_code} - {response.text}")
            return

        file_list = response.json()
        print(
            f"文件列表: 共 {file_list['total']} 个文件, 总大小 {file_list['total_size']} 字节"
        )
        for file_info in file_list["files"]:
            print(f"  - {file_info['filename']} ({file_info['file_size']} 字节)")

        # 4. 下载文件 - 使用file_id
        print("\n4. 下载文件 - 使用file_id...")
        file_id = upload_result["file_id"]
        response = await client.get(f"{BASE_URL}/sessions/{session_id}/files/{file_id}")

        if response.status_code != 200:
            print(f"文件下载失败: {response.status_code} - {response.text}")
            return

        # 保存下载的文件
        download_path = "downloaded_test.txt"
        with open(download_path, "wb") as f:
            f.write(response.content)

        print(f"文件下载成功: {download_path}")
        print(f"使用的file_id: {file_id}")

        # 5. 验证下载的文件内容
        print("\n5. 验证文件内容...")
        with open(test_file_path, "r") as original:
            original_content = original.read()

        with open(download_path, "r") as downloaded:
            downloaded_content = downloaded.read()

        if original_content == downloaded_content:
            print("✓ 文件内容验证成功")
        else:
            print("✗ 文件内容验证失败")

        # 6. 删除文件 - 使用file_id
        print("\n6. 删除文件 - 使用file_id...")
        file_id = upload_result["file_id"]
        response = await client.delete(
            f"{BASE_URL}/sessions/{session_id}/files/{file_id}"
        )

        if response.status_code != 200:
            print(f"文件删除失败: {response.status_code} - {response.text}")
            return

        print("文件删除成功")
        print(f"使用的file_id: {file_id}")

        # 7. 验证文件已被删除
        print("\n7. 验证文件删除...")
        response = await client.get(f"{BASE_URL}/sessions/{session_id}/files")

        if response.status_code == 200:
            file_list = response.json()
            active_files = [f for f in file_list["files"] if f["status"] == "active"]
            if len(active_files) == 0:
                print("✓ 文件已成功删除")
            else:
                print("✗ 文件仍然存在")

        # 8. 显示所有文件记录（包括已删除的）
        print("\n8. 显示所有文件记录（包括已删除的）...")
        response = await client.get(
            f"{BASE_URL}/sessions/{session_id}/files?status=all"
        )

        if response.status_code == 200:
            file_list = response.json()
            print(f"所有文件记录（共 {file_list['total']} 个）:")
            for file_info in file_list["files"]:
                status_text = "活跃" if file_info["status"] == "active" else "已删除"
                print(
                    f"  • {file_info['filename']} - {status_text} (ID: {file_info['id']})"
                )

        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        if os.path.exists(download_path):
            os.remove(download_path)

        print("\n✓ 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(test_file_management())
