#!/usr/bin/env python3
"""
生成修复后的Mermaid图表
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils.config_loader import ConfigLoader
from utils.mermaid_generator import MermaidFlowGenerator


def generate_fixed_state_diagram():
    """生成修复后的状态图"""
    print("🎨 生成修复后的Mermaid状态图")
    print("=" * 60)

    try:
        # 加载配置
        flow_config = ConfigLoader.load_flow_config(
            "config/flow_config/simple_test_flow.yaml"
        )
        generator = MermaidFlowGenerator(flow_config)

        # 生成状态图
        state_diagram = generator.generate_state_diagram()

        print(f"✅ 状态图生成成功")
        print(f"📊 图表长度: {len(state_diagram)} 字符")

        return state_diagram

    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return None


if __name__ == "__main__":
    diagram = generate_fixed_state_diagram()
    if diagram:
        print(f"\n📋 生成的状态图:")
        print(diagram)
