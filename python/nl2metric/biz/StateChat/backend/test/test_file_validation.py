#!/usr/bin/env python3
"""
文件验证功能测试脚本
"""

import asyncio
import os
import tempfile
from datetime import datetime
from pathlib import Path


# 模拟必要的导入和类
class MockFileUpload:
    def __init__(self, filename, file_path, file_size, content_type):
        self.filename = filename
        self.file_path = file_path
        self.file_size = file_size
        self.content_type = content_type
        self.upload_time = datetime.now()


class MockFlowContext:
    def __init__(self):
        self.uploaded_files = {}

    def add_uploaded_file(self, field_name, file_upload):
        if field_name not in self.uploaded_files:
            self.uploaded_files[field_name] = []
        self.uploaded_files[field_name].append(file_upload)


class MockStateNode:
    def __init__(self, name, file_validation=None):
        self.name = name
        self.file_validation = file_validation


async def test_file_validation():
    """测试文件验证功能"""
    print("开始测试文件验证功能...")

    # 导入文件验证器
    try:
        from core.file_validator import default_file_validator

        print("✓ 成功导入文件验证器")
    except ImportError as e:
        print(f"✗ 导入文件验证器失败: {e}")
        return False

    # 创建测试文件
    with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
        tmp.write(b"fake image content")
        test_file_path = tmp.name

    try:
        # 创建模拟数据
        context = MockFlowContext()
        file_upload = MockFileUpload(
            filename="test_session_123_身份证照片_20250729_163407.jpg",
            file_path=test_file_path,
            file_size=1024,
            content_type="image/jpeg",
        )
        context.add_uploaded_file("nric_or_passport_photo", file_upload)

        # 创建验证配置
        validation_config = {
            "allowed_types": ["image/jpeg", "image/png"],
            "max_size_mb": 10,
            "required_files": [
                {
                    "name_pattern": ".*身份证.*|.*NRIC.*|.*护照.*|.*Passport.*",
                    "description": "身份证或护照照片",
                    "min_count": 1,
                }
            ],
            "enable_multimodal": False,
        }

        state = MockStateNode(name="verify_identity", file_validation=validation_config)

        # 执行验证
        session_id = "test_session_123"
        result = await default_file_validator.validate_files(session_id, state, context)

        # 检查结果
        print(f"\n验证结果:")
        print(f"  会话ID: {result.session_id}")
        print(f"  状态名称: {result.state_name}")
        print(f"  总文件数: {result.total_files}")
        print(f"  有效文件数: {result.valid_files}")
        print(f"  无效文件数: {result.invalid_files}")
        print(f"  是否全部有效: {result.is_valid}")

        if result.errors:
            print(f"  错误信息:")
            for error in result.errors:
                print(f"    - {error.filename}: {error.error_message}")
                if error.suggestion:
                    print(f"      建议: {error.suggestion}")

        if result.validation_results:
            print(f"  验证详情:")
            for vr in result.validation_results:
                print(f"    - {vr.validation_type}: {vr.status} - {vr.message}")

        # 测试建议功能
        suggestions = await default_file_validator.get_file_validation_suggestions(
            result
        )
        if suggestions:
            print(f"  改进建议:")
            for suggestion in suggestions:
                print(f"    - {suggestion}")

        # 判断测试是否成功
        success = result.is_valid and result.total_files > 0
        print(f"\n测试结果: {'✓ 成功' if success else '✗ 失败'}")

        return success

    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


async def test_multimodal_validation():
    """测试多模态验证功能"""
    print("\n开始测试多模态验证功能...")

    try:
        from core.file_validator import default_file_validator

        # 创建测试图片文件
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
            tmp.write(b"fake image content for multimodal testing")
            test_file_path = tmp.name

        try:
            file_upload = MockFileUpload(
                filename="test_multimodal.jpg",
                file_path=test_file_path,
                file_size=1024,
                content_type="image/jpeg",
            )

            state = MockStateNode(name="test_state")

            # 创建多模态验证配置
            from models.file_validation import MultimodalValidationConfig

            multimodal_config = MultimodalValidationConfig(
                enabled=True,
                confidence_threshold=0.8,
                content_types=["身份证", "护照"],
                auto_validate=True,
            )

            # 执行多模态验证
            result = await default_file_validator.validate_with_multimodal(
                file_upload, state, multimodal_config
            )

            print(f"多模态验证结果:")
            print(f"  验证类型: {result.validation_type}")
            print(f"  验证状态: {result.status}")
            print(f"  验证消息: {result.message}")

            return True

        finally:
            if os.path.exists(test_file_path):
                os.remove(test_file_path)

    except Exception as e:
        print(f"✗ 多模态验证测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("=" * 50)
    print("文件验证功能测试")
    print("=" * 50)

    # 测试基础文件验证
    test1_passed = await test_file_validation()

    # 测试多模态验证
    test2_passed = await test_multimodal_validation()

    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"  基础文件验证: {'✓ 通过' if test1_passed else '✗ 失败'}")
    print(f"  多模态验证: {'✓ 通过' if test2_passed else '✗ 失败'}")

    overall_success = test1_passed and test2_passed
    print(f"  总体结果: {'✓ 所有测试通过' if overall_success else '✗ 部分测试失败'}")
    print("=" * 50)

    return overall_success


if __name__ == "__main__":
    asyncio.run(main())
