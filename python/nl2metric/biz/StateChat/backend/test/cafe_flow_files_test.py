"""
咖啡厅注册流程文件上传测试
测试各种文件上传和验证功能
"""

import asyncio
import tempfile
import os
from typing import Dict, List, Optional
from datetime import datetime

from test.common_util import StateMachineAPIClient, verify_session_in_db
from utils.app_config import settings


class CafeFlowFilesTestData:
    """文件上传测试数据工厂"""

    @staticmethod
    def create_test_image(filename: str, size_kb: int = 100) -> tuple:
        """创建测试图片文件"""
        content = b"fake_image_content_" + b"x" * (size_kb * 1024 - 20)
        file_path = os.path.join(tempfile.gettempdir(), filename)
        with open(file_path, "wb") as f:
            f.write(content)
        return file_path, filename

    @staticmethod
    def create_test_pdf(filename: str, size_kb: int = 200) -> tuple:
        """创建测试PDF文件"""
        content = b"fake_pdf_content_" + b"x" * (size_kb * 1024 - 19)
        file_path = os.path.join(tempfile.gettempdir(), filename)
        with open(file_path, "wb") as f:
            f.write(content)
        return file_path, filename

    @staticmethod
    def create_large_file(filename: str, size_mb: int = 15) -> tuple:
        """创建大文件测试用"""
        content = b"large_file_content_" + b"x" * (size_mb * 1024 * 1024 - 20)
        file_path = os.path.join(tempfile.gettempdir(), filename)
        with open(file_path, "wb") as f:
            f.write(content)
        return file_path, filename

    @staticmethod
    def create_invalid_file(filename: str, extension: str = "txt") -> tuple:
        """创建无效文件类型测试用"""
        content = b"invalid_file_content"
        file_path = os.path.join(tempfile.gettempdir(), f"{filename}.{extension}")
        with open(file_path, "wb") as f:
            f.write(content)
        return file_path, f"{filename}.{extension}"


class CafeFlowFilesTest:
    """咖啡厅注册流程文件上传测试"""

    def __init__(self):
        self.client = StateMachineAPIClient()
        self.test_files = {}
        self.session_id = None
        self.flow_name = "cafe_license_application"

    async def setup_test_files(self):
        """设置测试文件"""
        print("\n📁 准备文件上传测试文件...")

        # 创建各种测试文件
        test_files_data = {
            "valid_id_card": CafeFlowFilesTestData.create_test_image(
                "张三身份证.jpg", 50
            ),
            "valid_uen_doc": CafeFlowFilesTestData.create_test_pdf(
                "UEN认证文件.pdf", 150
            ),
            "valid_floor_plan": CafeFlowFilesTestData.create_test_pdf(
                "店铺平面图.pdf", 300
            ),
            "valid_tenancy": CafeFlowFilesTestData.create_test_pdf("租赁协议.pdf", 200),
            "large_file": CafeFlowFilesTestData.create_large_file("大文件测试.pdf", 15),
            "invalid_file": CafeFlowFilesTestData.create_invalid_file(
                "无效文件", "txt"
            ),
            "valid_acra_bizfile": CafeFlowFilesTestData.create_test_pdf(
                "ACRA商业档案.pdf", 400
            ),
            "valid_signboard": CafeFlowFilesTestData.create_test_image(
                "招牌设计.jpg", 250
            ),
            "valid_food_hygiene": CafeFlowFilesTestData.create_test_pdf(
                "食品卫生证书.pdf", 180
            ),
        }

        for key, (file_path, filename) in test_files_data.items():
            self.test_files[key] = (file_path, filename)
            print(f"   ✅ 创建测试文件: {filename}")

    async def cleanup_test_files(self):
        """清理测试文件"""
        print("\n🧹 清理文件上传测试文件...")
        for key, (file_path, filename) in self.test_files.items():
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"   ✅ 清理: {filename}")
            except Exception as e:
                print(f"   ⚠️  清理失败 {filename}: {e}")

    async def test_identity_verification_files(self):
        """测试身份验证文件上传"""
        print("\n📋 测试身份验证文件上传")

        # 创建会话并推进到身份验证状态
        result = await self.client.create_session("test_files_identity", self.flow_name)
        session_id = result["session_id"]

        try:
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("还没有租")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "verify_identity"

            # 上传身份证文件
            id_card_path, id_card_filename = self.test_files["valid_id_card"]
            await self.client.upload_file(id_card_path, id_card_filename)

            # 上传UEN文件
            uen_path, uen_filename = self.test_files["valid_uen_doc"]
            await self.client.upload_file(uen_path, uen_filename)

            print("   ✅ 身份验证文件上传测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_file_validation_errors(self):
        """测试文件验证错误处理"""
        print("\n📋 测试文件验证错误处理")

        result = await self.client.create_session(
            "test_files_validation", self.flow_name
        )
        session_id = result["session_id"]

        try:
            # 推进到文件上传状态
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("还没有租")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "verify_identity"

            # 测试上传无效文件类型
            invalid_path, invalid_filename = self.test_files["invalid_file"]
            print(f"   🧪 测试上传无效文件类型: {invalid_filename}")
            try:
                await self.client.upload_file(invalid_path, invalid_filename)
                print("   ⚠️  期望文件验证失败但实际成功了")
            except Exception as e:
                print(f"   ✅ 文件验证正确拒绝: {e}")

            # 测试上传大文件
            large_path, large_filename = self.test_files["large_file"]
            print(f"   🧪 测试上传大文件: {large_filename}")
            try:
                await self.client.upload_file(large_path, large_filename)
                print("   ⚠️  期望大文件被拒绝但实际成功了")
            except Exception as e:
                print(f"   ✅ 大文件验证正确拒绝: {e}")

            print("   ✅ 文件验证错误处理测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_multiple_file_uploads(self):
        """测试多文件上传场景"""
        print("\n📋 测试多文件上传场景")

        result = await self.client.create_session("test_files_multiple", self.flow_name)
        session_id = result["session_id"]

        try:
            # 推进到食品店许可证流程
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "check_food_shop_license"

            await self.client.send_input("否")
            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "food_shop_license_flow"

            # 上传多个食品店许可证相关文件
            floor_plan_path, floor_plan_filename = self.test_files["valid_floor_plan"]
            await self.client.upload_file(floor_plan_path, floor_plan_filename)

            tenancy_path, tenancy_filename = self.test_files["valid_tenancy"]
            await self.client.upload_file(tenancy_path, tenancy_filename)

            food_hygiene_path, food_hygiene_filename = self.test_files[
                "valid_food_hygiene"
            ]
            await self.client.upload_file(food_hygiene_path, food_hygiene_filename)

            print("   ✅ 多文件上传测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_file_upload_in_different_states(self):
        """测试不同状态下的文件上传"""
        print("\n📋 测试不同状态下的文件上传")

        # 测试招牌许可证文件上传
        result = await self.client.create_session(
            "test_files_signboard", self.flow_name
        )
        session_id = result["session_id"]

        try:
            # 快速推进到招牌许可证流程
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("已经租了")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("是")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "signboard_license_flow"

            # 上传招牌设计文件
            signboard_path, signboard_filename = self.test_files["valid_signboard"]
            await self.client.upload_file(signboard_path, signboard_filename)

            print("   ✅ 招牌许可证文件上传测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def test_file_upload_context_persistence(self):
        """测试文件上传后上下文持久化"""
        print("\n📋 测试文件上传上下文持久化")

        result = await self.client.create_session("test_files_context", self.flow_name)
        session_id = result["session_id"]

        try:
            # 推进到身份验证状态
            await self.client.send_input("我想申请咖啡店营业执照")
            await self.client.send_input("是")
            await self.client.send_input("还没有租")
            await self.client.send_input(
                "单位地址：乌节路123号\n租赁协议：已签署\n平面图：已准备"
            )
            await self.client.send_input("无")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")
            await self.client.send_input("否")

            state_info = await self.client.get_current_state()
            assert state_info["current_state"] == "verify_identity"

            # 上传文件
            id_card_path, id_card_filename = self.test_files["valid_id_card"]
            await self.client.upload_file(id_card_path, id_card_filename)

            # 获取会话信息验证上下文
            session_info = await self.client.get_session_info()
            context = session_info["context"]

            # 验证文件信息保存在上下文中
            assert "uploaded_files" in context
            assert len(context["uploaded_files"]) > 0

            # 查找上传的文件
            found_file = False
            for field_files in context["uploaded_files"].values():
                for file_info in field_files:
                    if file_info["filename"] == id_card_filename:
                        found_file = True
                        assert file_info["file_size"] > 0
                        assert "upload_time" in file_info
                        break

            assert found_file, "上传的文件未在上下文中找到"

            print("   ✅ 文件上传上下文持久化测试通过")

        finally:
            # 清理会话
            try:
                await self.client.session_store.delete_session(session_id)
            except:
                pass

    async def run_all_files_tests(self):
        """运行所有文件上传测试"""
        print("\n" + "=" * 60)
        print("📁 开始咖啡厅注册流程文件上传测试")
        print("=" * 60)

        test_methods = [
            self.test_identity_verification_files,
            self.test_file_validation_errors,
            self.test_multiple_file_uploads,
            self.test_file_upload_in_different_states,
            self.test_file_upload_context_persistence,
        ]

        passed_tests = 0
        total_tests = len(test_methods)

        for test_method in test_methods:
            try:
                await test_method()
                passed_tests += 1
            except Exception as e:
                print(f"\n❌ 测试失败: {test_method.__name__} - {e}")
                # 继续执行其他测试

        print(f"\n📊 文件上传测试结果: {passed_tests}/{total_tests} 通过")
        return passed_tests == total_tests


async def main():
    """主测试函数"""
    print("🚀 开始咖啡厅注册流程文件上传测试")

    test = CafeFlowFilesTest()

    try:
        # 健康检查
        await test.client.health_check()

        # 准备测试文件
        await test.setup_test_files()

        # 运行所有文件上传测试
        success = await test.run_all_files_tests()

        if success:
            print("\n🎉🎉🎉 所有文件上传测试均已通过! 🎉🎉🎉")
        else:
            print("\n❌ 部分文件上传测试失败")
            return False

    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        return False
    finally:
        # 清理测试文件
        await test.cleanup_test_files()

    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
