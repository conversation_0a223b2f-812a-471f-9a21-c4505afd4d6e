"""
咖啡厅注册流程完整测试套件运行器
统一运行所有咖啡厅流程相关的测试
"""

import asyncio
import sys
import traceback
from datetime import datetime
from typing import List, Dict, Any

# 导入测试模块
from test.cafe_flow_complete_test import CafeFlowCompleteTest
from test.cafe_flow_branches_test import CafeFlowBranchesTest
from test.cafe_flow_files_test import CafeFlowFilesTest
from test.cafe_flow_smoke_test import run_cafe_flow_test


class CafeFlowTestRunner:
    """咖啡厅流程测试运行器"""

    def __init__(self):
        self.test_results = []
        self.start_time = None
        self.end_time = None

    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有咖啡厅流程测试"""
        self.start_time = datetime.now()

        print("\n" + "=" * 80)
        print("🎯 咖啡厅注册流程完整测试套件")
        print("=" * 80)
        print(f"📅 测试开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📋 测试套件包含:")
        print("  1. 基础冒烟测试 (Smoke Test)")
        print("  2. 完整流程测试 (Complete Flow Test)")
        print("  3. 分支路径测试 (Branch Tests)")
        print("  4. 文件上传测试 (File Upload Tests)")
        print("=" * 80)

        test_suites = [
            ("基础冒烟测试", self.run_smoke_test),
            ("完整流程测试", self.run_complete_test),
            ("分支路径测试", self.run_branch_tests),
            ("文件上传测试", self.run_files_test),
        ]

        total_suites = len(test_suites)
        passed_suites = 0

        for suite_name, suite_func in test_suites:
            print(f"\n🚀 执行测试套件: {suite_name}")
            print("-" * 60)

            try:
                success = await suite_func()
                if success:
                    passed_suites += 1
                    print(f"✅ {suite_name} - 通过")
                else:
                    print(f"❌ {suite_name} - 失败")
            except Exception as e:
                print(f"❌ {suite_name} - 异常: {e}")
                traceback.print_exc()

        self.end_time = datetime.now()
        duration = self.end_time - self.start_time

        # 生成测试报告
        report = self.generate_test_report(total_suites, passed_suites, duration)

        print("\n" + "=" * 80)
        print("📊 测试执行报告")
        print("=" * 80)
        print(f"📅 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 结束时间: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  执行时长: {duration}")
        print(f"📈 测试套件: {passed_suites}/{total_suites} 通过")
        print(f"🎯 成功率: {(passed_suites / total_suites) * 100:.1f}%")

        if passed_suites == total_suites:
            print("\n🎉🎉🎉 所有测试套件均已通过! 🎉🎉🎉")
        else:
            print(f"\n⚠️  有 {total_suites - passed_suites} 个测试套件失败")

        print("=" * 80)

        return report

    async def run_smoke_test(self) -> bool:
        """运行基础冒烟测试"""
        try:
            await run_cafe_flow_test()
            return True
        except Exception as e:
            print(f"❌ 冒烟测试失败: {e}")
            return False

    async def run_complete_test(self) -> bool:
        """运行完整流程测试"""
        test = CafeFlowCompleteTest()
        try:
            # 健康检查
            await test.client.health_check()

            # 准备测试文件
            await test.setup_test_files()

            # 运行完整测试
            success = await test.run_complete_test()

            return success

        except Exception as e:
            print(f"❌ 完整流程测试失败: {e}")
            return False
        finally:
            # 清理测试文件
            await test.cleanup_test_files()

    async def run_branch_tests(self) -> bool:
        """运行分支路径测试"""
        test = CafeFlowBranchesTest()
        try:
            # 健康检查
            await test.client.health_check()

            # 准备测试文件
            await test.setup_test_files()

            # 运行分支测试
            success = await test.run_all_branch_tests()

            return success

        except Exception as e:
            print(f"❌ 分支路径测试失败: {e}")
            return False
        finally:
            # 清理测试文件
            await test.cleanup_test_files()

    async def run_files_test(self) -> bool:
        """运行文件上传测试"""
        test = CafeFlowFilesTest()
        try:
            # 健康检查
            await test.client.health_check()

            # 准备测试文件
            await test.setup_test_files()

            # 运行文件上传测试
            success = await test.run_all_files_tests()

            return success

        except Exception as e:
            print(f"❌ 文件上传测试失败: {e}")
            return False
        finally:
            # 清理测试文件
            await test.cleanup_test_files()

    def generate_test_report(
        self, total_suites: int, passed_suites: int, duration
    ) -> Dict[str, Any]:
        """生成测试报告"""
        return {
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "duration_seconds": duration.total_seconds(),
            "total_test_suites": total_suites,
            "passed_test_suites": passed_suites,
            "failed_test_suites": total_suites - passed_suites,
            "success_rate": (passed_suites / total_suites) * 100,
            "status": "PASSED" if passed_suites == total_suites else "FAILED",
        }


async def main():
    """主函数"""
    print("🚀 启动咖啡厅注册流程测试套件")

    runner = CafeFlowTestRunner()

    try:
        # 运行所有测试
        report = await runner.run_all_tests()

        # 根据测试结果设置退出码
        if report["status"] == "PASSED":
            print("\n✅ 测试套件执行成功")
            return True
        else:
            print("\n❌ 测试套件执行失败")
            return False

    except Exception as e:
        print(f"\n💥 测试套件执行异常: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
