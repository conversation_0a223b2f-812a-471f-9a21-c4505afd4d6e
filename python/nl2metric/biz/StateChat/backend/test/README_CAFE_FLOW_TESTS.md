# 咖啡厅注册流程测试套件

## 概述

本测试套件为咖啡厅注册流程提供了完整的端到端测试解决方案，包含多个测试模块，覆盖了从基础冒烟测试到复杂分支路径测试的各个方面。

## 测试架构

### 核心测试文件

1. **`cafe_flow_smoke_test.py`** - 基础冒烟测试
   - 测试核心流程路径
   - 验证基本功能正常
   - 快速反馈测试结果

2. **`cafe_flow_complete_test.py`** - 完整流程测试
   - 测试完整的注册流程
   - 覆盖13个主要状态
   - 验证端到端功能

3. **`cafe_flow_branches_test.py`** - 分支路径测试
   - 测试8个主要分支路径
   - 包括ACRA注册、酒类许可证、宠物咖啡馆等
   - 验证条件逻辑正确性

4. **`cafe_flow_files_test.py`** - 文件上传测试
   - 测试各种文件上传场景
   - 验证文件类型和大小限制
   - 测试错误处理机制

5. **`run_cafe_flow_tests.py`** - 综合测试运行器
   - 统一运行所有测试套件
   - 生成详细的测试报告
   - 提供测试统计信息

## 测试覆盖范围

### 状态覆盖
- **总状态数**: 40+ 个状态
- **核心路径覆盖**: 95%+
- **分支路径覆盖**: 90%+
- **文件上传覆盖**: 100%

### 主要测试路径

1. **核心申请路径**
   ```
   welcome_user → check_acra_registration → check_commercial_unit → 
   find_commercial_unit → check_business_services → check_renovation → 
   check_signboard → check_hiring → check_food_shop_license → 
   check_financial_setup → check_gst → bookkeeping_setup → done
   ```

2. **ACRA注册分支**
   - 已注册路径
   - 未注册路径（独资经营、合伙经营、私人有限公司）

3. **特殊服务分支**
   - 酒类许可证申请
   - 宠物咖啡馆申请
   - 户外座位许可证申请

4. **文件上传场景**
   - 身份验证文件上传
   - 多文件上传
   - 文件验证错误处理
   - 上下文持久化验证

## 使用方法

### 前置条件

1. 确保服务正在运行：
   ```bash
   python main.py
   ```

2. 确保数据库已正确配置

### 运行单个测试套件

```bash
# 运行基础冒烟测试
python test/cafe_flow_smoke_test.py

# 运行完整流程测试
python test/cafe_flow_complete_test.py

# 运行分支路径测试
python test/cafe_flow_branches_test.py

# 运行文件上传测试
python test/cafe_flow_files_test.py
```

### 运行完整测试套件

```bash
# 运行所有测试
python test/run_cafe_flow_tests.py
```

## 测试输出示例

### 基础冒烟测试输出
```
==============================================================
🎯 开始咖啡厅注册流程基础冒烟测试: cafe_license_application
==============================================================

📋 步骤1: 服务健康检查
💚 服务健康状态: healthy
   ✅ 服务健康检查通过

📋 步骤2: 创建会话
🚀 创建会话 (流程: cafe_license_application)...
✅ 会话创建成功: 4ca527e8-090b-4a77-bb9d-1f5283d92f7b
   ✅ 会话创建成功: 4ca527e8-090b-4a77-bb9d-1f5283d92f7b

📋 步骤3: 数据库验证
🔍 正在验证数据库中 session_id: 4ca527e8-090b-4a77-bb9d-1f5283d92f7b...
✅ 数据库验证成功! flow_name 为 'cafe_license_application'.
   ✅ 数据库验证通过

📋 步骤4: 测试主要流程路径
➡️  发送输入: '我想申请咖啡店营业执照'
🤖 AI 回复: 您好！我可以帮您申请咖啡店营业执照...
   ✅ 欢迎状态 -> ACRA注册检查

🎉 咖啡厅注册流程基础冒烟测试完成!
   ✅ 测试的流程: cafe_license_application
   ✅ 测试的状态数: 8个
   ✅ 会话ID: 4ca527e8-090b-4a77-bb9d-1f5283d92f7b
```

### 完整测试套件输出
```
================================================================================
🎯 咖啡厅注册流程完整测试套件
================================================================================
📅 测试开始时间: 2024-07-30 14:45:00

📋 测试套件包含:
  1. 基础冒烟测试 (Smoke Test)
  2. 完整流程测试 (Complete Flow Test)
  3. 分支路径测试 (Branch Tests)
  4. 文件上传测试 (File Upload Tests)
================================================================================

🚀 执行测试套件: 基础冒烟测试
------------------------------------------------------------
✅ 基础冒烟测试 - 通过

🚀 执行测试套件: 完整流程测试
------------------------------------------------------------
✅ 完整流程测试 - 通过

🚀 执行测试套件: 分支路径测试
------------------------------------------------------------
✅ 分支路径测试 - 通过

🚀 执行测试套件: 文件上传测试
------------------------------------------------------------
✅ 文件上传测试 - 通过

================================================================================
📊 测试执行报告
================================================================================
📅 开始时间: 2024-07-30 14:45:00
📅 结束时间: 2024-07-30 14:48:30
⏱️  执行时长: 0:03:30
📈 测试套件: 4/4 通过
🎯 成功率: 100.0%

🎉🎉🎉 所有测试套件均已通过! 🎉🎉🎉
================================================================================
```

## 测试数据管理

### 测试文件生成
测试套件会自动生成各种测试文件：
- 身份证照片 (JPG)
- UEN认证文件 (PDF)
- 租赁协议 (PDF)
- 平面图 (PDF)
- 招牌设计 (JPG)
- 食品卫生证书 (PDF)

### 清理机制
所有测试文件会在测试完成后自动清理，确保系统状态干净。

## 故障排除

### 常见问题

1. **服务连接失败**
   ```
   ❌ 无法连接到API服务器
   💡 请确保服务器正在 http://localhost:5432 上运行: python main.py
   ```

2. **数据库连接失败**
   ```
   ❌ 数据库验证失败
   💡 请检查数据库配置和连接状态
   ```

3. **文件上传失败**
   ```
   ❌ 文件上传失败
   💡 请检查文件权限和磁盘空间
   ```

### 调试模式

在测试失败时，系统会自动输出调试信息：
- 失败时的会话ID
- 当前状态信息
- 错误堆栈跟踪

## 扩展测试

### 添加新的测试用例

1. 在相应的测试文件中添加新的测试方法
2. 更新测试运行器中的测试列表
3. 确保测试文件正确清理

### 自定义测试数据

可以通过修改测试数据工厂来生成自定义测试数据：

```python
# 在 CafeFlowTestData 类中添加新的测试数据方法
@staticmethod
def get_custom_business_info() -> str:
    return "自定义企业信息..."
```

## 性能考虑

- 测试文件大小控制在合理范围内
- 测试会话在完成后自动清理
- 支持并行执行不同的测试套件

