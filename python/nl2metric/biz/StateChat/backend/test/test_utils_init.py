import pytest
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime
from utils import flatten_str
from models.context import (
    FlowContext,
    FormData,
    FileUpload,
    MessageRole as ContextMessageRole,
    ChatMessage as ContextChatMessage,
)

# --- Test Models ---


class MessageRole(str, Enum):
    """消息角色枚举"""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class Address(BaseModel):
    """地址模型"""

    street: str = Field(description="街道")
    city: str = Field(description="城市")
    zip_code: Optional[str] = Field(None, description="邮政编码")


class Person(BaseModel):
    """人员模型"""

    name: str = Field(description="姓名")
    age: int = Field(description="年龄")
    address: Address = Field(description="地址")
    nicknames: List[str] = Field(default_factory=list, description="昵称列表")


class Company(BaseModel):
    """公司模型"""

    name: str = Field(description="公司名称")
    employees: List[Person] = Field(description="员工列表")


class ChatMessage(BaseModel):
    """聊天消息模型"""

    content: str = Field(description="消息内容")
    role: MessageRole = Field(description="消息角色")


class ChatSession(BaseModel):
    """聊天会话模型"""

    session_id: str = Field(description="会话ID")
    messages: List[ChatMessage] = Field(description="消息列表")
    current_role: MessageRole = Field(description="当前角色")


# --- Test Cases ---


def test_flatten_str_simple_model():
    """测试简单模型的扁平化"""

    class Simple(BaseModel):
        id: int = Field(description="ID")
        description: str = Field(description="描述")

    model_instance = Simple(id=1, description="一个简单的测试")
    result = flatten_str(model_instance)

    expected_yaml = "ID: 1\n描述: 一个简单的测试\n"
    assert result == expected_yaml


def test_flatten_str_model_with_list():
    """测试包含列表的模型"""

    class WithList(BaseModel):
        name: str = Field(description="名称")
        tags: List[str] = Field(description="标签列表")

    model_instance = WithList(name="测试列表", tags=["a", "b", "c"])
    result = flatten_str(model_instance)

    expected_yaml = "名称: 测试列表\n标签列表:\n- a\n- b\n- c\n"
    assert result == expected_yaml


def test_flatten_str_nested_model():
    """测试嵌套模型"""
    address_instance = Address(street="123 Main St", city="Anytown", zip_code="12345")
    person_instance = Person(
        name="张三", age=30, address=address_instance, nicknames=["阿三"]
    )
    result = flatten_str(person_instance)

    expected_yaml = (
        "姓名: 张三\n"
        "年龄: 30\n"
        "地址:\n"
        "  街道: 123 Main St\n"
        "  城市: Anytown\n"
        "  邮政编码: '12345'\n"
        "昵称列表:\n- 阿三\n"
    )
    assert result == expected_yaml


def test_flatten_str_model_with_list_of_models():
    """测试包含模型列表的模型"""
    address1 = Address(street="1 Apple Park Way", city="Cupertino", zip_code="95014")
    person1 = Person(name="库克", age=50, address=address1)

    address2 = Address(
        street="1600 Amphitheatre Parkway", city="Mountain View", zip_code="94043"
    )
    person2 = Person(name="拉里", age=55, address=address2)

    company_instance = Company(name="科技巨头", employees=[person1, person2])
    result = flatten_str(company_instance)

    expected_yaml = (
        "公司名称: 科技巨头\n"
        "员工列表:\n"
        "- 姓名: 库克\n"
        "  年龄: 50\n"
        "  地址:\n"
        "    街道: 1 Apple Park Way\n"
        "    城市: Cupertino\n"
        "    邮政编码: '95014'\n"
        "  昵称列表: []\n"
        "- 姓名: 拉里\n"
        "  年龄: 55\n"
        "  地址:\n"
        "    街道: 1600 Amphitheatre Parkway\n"
        "    城市: Mountain View\n"
        "    邮政编码: '94043'\n"
        "  昵称列表: []\n"
    )
    assert result.replace("'", "") == expected_yaml.replace("'", "")


def test_flatten_str_empty_and_none_values():
    """测试空值和None值"""

    class WithEmpty(BaseModel):
        name: str = Field(description="名称")
        optional_field: Optional[str] = Field(None, description="可选字段")
        empty_list: List[str] = Field(default_factory=list, description="空列表")

    model_instance = WithEmpty(name="测试空值")
    result = flatten_str(model_instance)

    expected_yaml = (
        "名称: 测试空值\n"
        "可选字段: null\n"  # YAML represents None as null
        "空列表: []\n"
    )
    assert result == expected_yaml


def test_flatten_str_no_description():
    """测试字段没有描述的情况"""

    class NoDesc(BaseModel):
        name: str  # No description
        value: int  # No description

    model_instance = NoDesc(name="无描述", value=123)
    result = flatten_str(model_instance)

    expected_yaml = "name: 无描述\nvalue: 123\n"
    assert result == expected_yaml


def test_flatten_str_with_enum():
    """测试包含 Enum 的模型"""
    message_instance = ChatMessage(content="你好", role=MessageRole.USER)
    result = flatten_str(message_instance)

    expected_yaml = "消息内容: 你好\n消息角色: user\n"
    assert result == expected_yaml

    session_instance = ChatSession(
        session_id="sess123",
        messages=[
            ChatMessage(content="你好", role=MessageRole.USER),
            ChatMessage(content="有什么可以帮助您的吗？", role=MessageRole.ASSISTANT),
        ],
        current_role=MessageRole.ASSISTANT,
    )
    result_session = flatten_str(session_instance)
    expected_session_yaml = (
        "会话ID: sess123\n"
        "消息列表:\n"
        "- 消息内容: 你好\n"
        "  消息角色: user\n"
        "- 消息内容: 有什么可以帮助您的吗？\n"
        "  消息角色: assistant\n"
        "当前角色: assistant\n"
    )
    assert result_session == expected_session_yaml


def test_flatten_str_with_flow_context():
    """测试包含 FlowContext 的模型"""
    # 创建一些嵌套的数据
    form_data_instance = FormData(
        field_name="service_a",
        field_value="文件已上传完成，请继续处理",
        field_type="file",
        is_required=False,
        validation_rules=None,
    )

    file_upload_instance = FileUpload(
        filename="服务A申请表.pdf",
        file_path="uploads/xxx/服务A申请表.pdf",
        file_size=1024,
        content_type="application/pdf",
    )

    chat_message_instance = ContextChatMessage(
        role=ContextMessageRole.USER, content="我想使用你们公司的服务A"
    )

    # 创建 FlowContext 实例
    flow_context_instance = FlowContext(
        session_id="session_abc_123",
        flow_name="service_a_flow",
        form_data={"service_a": form_data_instance},
        uploaded_files={"service_a": [file_upload_instance]},
        business_info={"company_name": "测试公司"},
        personal_info={"user_name": "张三"},
        license_info={"license_number": "LICENSE_001"},
        completed_steps=["step1", "step2"],
        pending_approvals=["approval_a"],
        generated_documents=["doc1.pdf"],
        chat_history=[chat_message_instance],
    )

    result = flatten_str(flow_context_instance)

    # 为了简化断言，我们只检查YAML字符串中是否包含关键的键和值
    # 因为完整的YAML结构可能非常复杂且难以精确预测
    assert "会话ID: session_abc_123" in result
    assert "流程名称: service_a_flow" in result
    assert "表单数据:" in result
    assert "字段名称: service_a" in result
    assert "字段值: 文件已上传完成，请继续处理" in result
    assert "上传文件:" in result
    assert "文件名: 服务A申请表.pdf" in result
    assert "企业信息:" in result
    assert "company_name: 测试公司" in result
    assert "个人信息:" in result
    assert "user_name: 张三" in result
    assert "许可证信息:" in result
    assert "license_number: LICENSE_001" in result
    assert "已完成步骤:" in result
    assert "- step1" in result
    assert "待审批项目:" in result
    assert "- approval_a" in result
    assert "生成的文档:" in result
    assert "- doc1.pdf" in result
    assert "聊天历史记录:" in result
    assert "消息角色: user" in result
    assert "消息内容: 我想使用你们公司的服务A" in result


if __name__ == "__main__":
    pytest.main([__file__])
