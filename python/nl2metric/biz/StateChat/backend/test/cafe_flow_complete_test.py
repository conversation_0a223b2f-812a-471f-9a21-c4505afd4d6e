"""
咖啡厅注册流程完整e2e测试
覆盖主要申请路径：ACRA已注册 → 商业单位 → 无特殊服务 → 完整申请流程
"""

import asyncio
import tempfile
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from test.common_util import StateMachineAPIClient, verify_session_in_db
from utils.app_config import settings


class CafeFlowTestData:
    """咖啡厅流程测试数据工厂"""

    @staticmethod
    def get_business_info() -> str:
        """获取企业信息测试数据"""
        return """
        拟用公司名称：星巴克咖啡(新加坡)私人有限公司
        业务性质：咖啡饮品及轻食销售
        营业地址：乌节路313号，乌节门购物中心#01-01，新加坡238895
        计划开业日期：2024年6月1日
        """

    @staticmethod
    def get_owner_info() -> str:
        """获取企业负责人信息测试数据"""
        return """
        全名：张三
        身份证号码：S1234567D
        国籍：新加坡
        联系电话：+65 9123 4567
        电子邮件：<EMAIL>
        职位：董事总经理
        """

    @staticmethod
    def get_director_info() -> str:
        """获取董事信息测试数据"""
        return f"{CafeFlowTestData.get_business_info()}\n{CafeFlowTestData.get_owner_info()}\n初始实缴资本：SGD 50,000\n股份分配：张三持有100%股份"

    @staticmethod
    def get_commercial_unit_info() -> str:
        """获取商业单位信息测试数据"""
        return """
        单位地址：乌节路313号，乌节门购物中心#01-01，新加坡238895
        租赁协议：已签署2年租约，月租SGD 8,000
        平面图：标准咖啡厅布局，包含80个座位
        """

    @staticmethod
    def get_financial_info() -> str:
        """获取财务信息测试数据"""
        return """
        ACRA商业档案：已准备UEN文件
        负责人身份证明：身份证复印件已准备
        公司章程：私人有限公司章程已准备
        地址证明：水电费账单已准备
        """

    @staticmethod
    def get_business_plan() -> str:
        """获取商业计划测试数据"""
        return """
        预计年收入：SGD 1,200,000
        客流量预估：日均200人次
        客单价：SGD 15-20
        营业时间：每日7:00-22:00
        员工人数：12人
        月固定成本：SGD 35,000
        预计利润率：25%
        """


class CafeFlowCompleteTest:
    """咖啡厅注册流程完整e2e测试"""

    def __init__(self):
        self.client = StateMachineAPIClient()
        self.test_files = {}
        self.session_id = None
        self.flow_name = "cafe_license_application"

    async def setup_test_files(self):
        """设置测试文件"""
        print("\n📁 准备测试文件...")

        # 创建各种测试文件
        test_files_data = {
            "id_card": ("张三身份证.jpg", b"fake_id_card_image_content"),
            "uen_document": ("UEN认证文件.pdf", b"fake_uen_document_content"),
            "tenancy_agreement": ("租赁协议.pdf", b"fake_tenancy_agreement_content"),
            "floor_plan": ("店铺平面图.pdf", b"fake_floor_plan_content"),
            "acra_bizfile": ("ACRA商业档案.pdf", b"fake_acra_bizfile_content"),
            "business_plan": ("商业计划书.pdf", b"fake_business_plan_content"),
            "bank_documents": ("银行开户文件.pdf", b"fake_bank_documents_content"),
        }

        for key, (filename, content) in test_files_data.items():
            file_path = os.path.join(tempfile.gettempdir(), f"test_{filename}")
            with open(file_path, "wb") as f:
                f.write(content)
            self.test_files[key] = (file_path, filename)
            print(f"   ✅ 创建测试文件: {filename}")

    async def cleanup_test_files(self):
        """清理测试文件"""
        print("\n🧹 清理测试文件...")
        for key, (file_path, filename) in self.test_files.items():
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"   ✅ 清理: {filename}")
            except Exception as e:
                print(f"   ⚠️  清理失败 {filename}: {e}")

    async def run_complete_test(self):
        """运行完整的咖啡厅注册流程测试"""
        print("\n" + "=" * 60)
        print("🎯 开始咖啡厅注册流程完整e2e测试")
        print("=" * 60)

        try:
            # 1. 创建会话
            await self.test_create_session()

            # 2. ACRA注册检查（已注册）
            await self.test_acra_registered()

            # 3. 商业单位检查（未租赁）
            await self.test_no_commercial_unit()

            # 4. 提供商业单位信息
            await self.test_provide_commercial_unit_info()

            # 5. 业务服务检查（无特殊服务）
            await self.test_no_special_services()

            # 6. 装修检查（无装修）
            await self.test_no_renovation()

            # 7. 招牌检查（无招牌）
            await self.test_no_signboard()

            # 8. 员工招聘检查（无员工）
            await self.test_no_hiring()

            # 9. 食品店许可证检查（未申请）
            await self.test_no_food_shop_license()

            # 10. 财务设置检查（无银行账户）
            await self.test_no_bank_account()

            # 11. GST检查（年收入低于100万）
            await self.test_no_gst()

            # 12. 簿记设置（需要帮助）
            await self.test_bookkeeping_help()

            # 13. 验证完成状态
            await self.test_completion()

            print("\n🎉 咖啡厅注册流程完整e2e测试通过!")
            return True

        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            if self.session_id:
                await self.print_session_debug_info()
            return False

    async def test_create_session(self):
        """测试创建会话"""
        print("\n📋 步骤1: 创建会话")

        result = await self.client.create_session("cafe_test_user", self.flow_name)
        self.session_id = result["session_id"]

        assert result["flow_name"] == self.flow_name
        assert result["current_state"] == "welcome_user"

        await verify_session_in_db(self.session_id, self.flow_name)
        print("   ✅ 会话创建成功")

    async def test_acra_registered(self):
        """测试ACRA已注册路径"""
        print("\n📋 步骤2: ACRA注册检查")

        await self.client.send_input("我想申请咖啡店营业执照")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_acra_registration"

        await self.client.send_input("是")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_commercial_unit"

        print("   ✅ ACRA已注册路径通过")

    async def test_no_commercial_unit(self):
        """测试未租赁商业单位路径"""
        print("\n📋 步骤3: 商业单位检查")

        await self.client.send_input("还没有租")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "find_commercial_unit"

        print("   ✅ 未租赁商业单位路径通过")

    async def test_provide_commercial_unit_info(self):
        """测试提供商业单位信息"""
        print("\n📋 步骤4: 提供商业单位信息")

        unit_info = CafeFlowTestData.get_commercial_unit_info()
        await self.client.send_input(unit_info)
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_business_services"

        print("   ✅ 商业单位信息提供成功")

    async def test_no_special_services(self):
        """测试无特殊服务路径"""
        print("\n📋 步骤5: 业务服务检查")

        await self.client.send_input("无")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_renovation"

        print("   ✅ 无特殊服务路径通过")

    async def test_no_renovation(self):
        """测试无装修路径"""
        print("\n📋 步骤6: 装修检查")

        await self.client.send_input("否")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_signboard"

        print("   ✅ 无装修路径通过")

    async def test_no_signboard(self):
        """测试无招牌路径"""
        print("\n📋 步骤7: 招牌检查")

        await self.client.send_input("否")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_hiring"

        print("   ✅ 无招牌路径通过")

    async def test_no_hiring(self):
        """测试无员工招聘路径"""
        print("\n📋 步骤8: 员工招聘检查")

        await self.client.send_input("否")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_food_shop_license"

        print("   ✅ 无员工招聘路径通过")

    async def test_no_food_shop_license(self):
        """测试未申请食品店许可证路径"""
        print("\n📋 步骤9: 食品店许可证检查")

        await self.client.send_input("否")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "food_shop_license_flow"

        # 模拟食品店许可证申请
        await self.client.send_input("平面图和租赁协议已准备")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "food_shop_submit_or_download"

        await self.client.send_input("提交")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_financial_setup"

        print("   ✅ 食品店许可证申请路径通过")

    async def test_no_bank_account(self):
        """测试无银行账户路径"""
        print("\n📋 步骤10: 财务设置检查")

        await self.client.send_input("否")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "open_business_bank_account"

        financial_info = CafeFlowTestData.get_financial_info()
        await self.client.send_input(financial_info)
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "check_gst"

        print("   ✅ 银行账户开设路径通过")

    async def test_no_gst(self):
        """测试无GST注册路径"""
        print("\n📋 步骤11: GST检查")

        await self.client.send_input("否")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "bookkeeping_setup"

        print("   ✅ 无GST注册路径通过")

    async def test_bookkeeping_help(self):
        """测试需要簿记帮助路径"""
        print("\n📋 步骤12: 簿记设置")

        await self.client.send_input("是")
        state_info = await self.client.get_current_state()
        assert state_info["current_state"] == "done"

        print("   ✅ 簿记帮助路径通过")

    async def test_completion(self):
        """测试完成状态"""
        print("\n📋 步骤13: 验证完成状态")

        session_info = await self.client.get_session_info()
        assert session_info["status"] == "completed"
        assert session_info["current_state"] == "done"

        print("   ✅ 流程完成状态验证通过")

    async def print_session_debug_info(self):
        """打印会话调试信息"""
        print("\n🔍 调试信息:")
        try:
            session_info = await self.client.get_session_info()
            print(f"   会话ID: {session_info['session_id']}")
            print(f"   流程名称: {session_info['flow_name']}")
            print(f"   当前状态: {session_info['current_state']}")
            print(f"   会话状态: {session_info['status']}")
        except Exception as e:
            print(f"   获取调试信息失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始咖啡厅注册流程完整e2e测试")

    test = CafeFlowCompleteTest()

    try:
        # 健康检查
        await test.client.health_check()

        # 准备测试文件
        await test.setup_test_files()

        # 运行完整测试
        success = await test.run_complete_test()

        if success:
            print("\n🎉🎉🎉 所有测试均已通过! 🎉🎉🎉")
        else:
            print("\n❌ 测试失败")
            return False

    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        return False
    finally:
        # 清理测试文件
        await test.cleanup_test_files()

    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
