#!/usr/bin/env python3
"""
测试新的进度计算方法
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils.config_loader import Config<PERSON>oader
from core.state_machine import StateMachine
from storage.database import db_manager


async def test_progress_calculation():
    """测试进度计算功能"""
    print("🧪 测试进度计算功能")
    print("=" * 60)
    # 初始化数据库
    await db_manager.init()
    # 加载流程配置
    config_loader = ConfigLoader()

    # 测试简单流程
    print("\n📊 测试 simple_test_flow")
    print("-" * 40)

    simple_flow = config_loader.load_flow_config(
        "config/flow_config/simple_test_flow.yaml"
    )
    if simple_flow:
        db_session = await db_manager.get_session()
        try:
            state_machine = StateMachine(simple_flow, db_session)

            # 测试各个状态的进度
            test_states = [
                "welcome_user",
                "check_registration",
                "register",
                "collect_personal_info",
                "collect_business_info",
                "main_process",
                "service_a",
                "service_b",
                "service_c",
                "done",
            ]

            print("状态名称 -> 深度 -> 进度%")
            for state in test_states:
                if state in simple_flow.states:
                    depth = state_machine._calculate_state_depth(state)
                    progress = state_machine.get_progress(state)
                    print(f"{state:20} -> {depth:2d} -> {progress:5.1f}%")

            # 测试流程深度估算
            estimated_depth = state_machine._estimate_total_flow_depth()
            print(f"\n估算总深度: {estimated_depth}")
            print(f"实际状态数: {len(simple_flow.states)}")
        finally:
            await db_session.close()

    # 测试复杂流程
    print("\n📊 测试 cafe_license_application")
    print("-" * 40)

    cafe_flow = config_loader.load_flow_config(
        "config/flow_config/cafe_license_flow.yaml"
    )
    if cafe_flow:
        db_session = await db_manager.get_session()
        try:
            state_machine = StateMachine(cafe_flow, db_session)

            # 测试关键路径状态
            key_states = [
                "welcome_user",
                "check_acra_registration",
                "register_acra",
                "collect_business_info_and_owner",
                "check_commercial_unit",
                "food_shop_license_flow",
                "check_hiring",
                "cpf_registration_flow",
                "check_food_shop_license",
                "done",
            ]

            print("状态名称 -> 深度 -> 进度%")
            for state in key_states:
                if state in cafe_flow.states:
                    depth = state_machine._calculate_state_depth(state)
                    progress = state_machine.get_progress(state)
                    print(f"{state:30} -> {depth:2d} -> {progress:5.1f}%")

            # 测试流程深度估算
            estimated_depth = state_machine._estimate_total_flow_depth()
            print(f"\n估算总深度: {estimated_depth}")
            print(f"实际状态数: {len(cafe_flow.states)}")
        finally:
            await db_session.close()


def test_path_analysis():
    """分析流程路径结构"""
    print("\n🔍 流程路径分析")
    print("=" * 60)

    config_loader = ConfigLoader()

    # 分析简单流程的路径
    print("\n📈 simple_test_flow 路径分析")
    print("-" * 40)

    simple_flow = config_loader.load_flow_config(
        "config/flow_config/simple_test_flow.yaml"
    )
    if simple_flow:
        print("流程结构:")
        for state_name, state_node in simple_flow.states.items():
            next_info = ""
            if isinstance(state_node.next, str):
                next_info = f" -> {state_node.next}"
            elif isinstance(state_node.next, dict):
                next_options = ", ".join(
                    [f"{k}:{v}" for k, v in state_node.next.items()]
                )
                next_info = f" -> [{next_options}]"
            elif state_node.next is None:
                next_info = " -> [END]"

            print(f"  {state_name}{next_info}")

        print(f"\n初始状态: {simple_flow.initial_state}")
        print(f"最终状态: {simple_flow.final_states}")

    # 分析复杂流程的关键路径
    print("\n📈 cafe_license_application 关键路径")
    print("-" * 40)

    cafe_flow = config_loader.load_flow_config(
        "config/flow_config/cafe_license_flow.yaml"
    )
    if cafe_flow:
        # 显示分支点
        branch_states = []
        linear_states = []
        final_states = []

        for state_name, state_node in cafe_flow.states.items():
            if isinstance(state_node.next, dict):
                branch_states.append(state_name)
            elif state_node.next is None:
                final_states.append(state_name)
            else:
                linear_states.append(state_name)

        print(f"分支状态 ({len(branch_states)}): {branch_states[:5]}...")
        print(f"线性状态 ({len(linear_states)}): {linear_states[:5]}...")
        print(f"最终状态 ({len(final_states)}): {final_states}")
        print(f"总状态数: {len(cafe_flow.states)}")


async def compare_old_vs_new():
    """比较旧方法和新方法的进度计算"""
    print("\n⚖️  旧方法 vs 新方法对比")
    print("=" * 60)

    config_loader = ConfigLoader()
    simple_flow = config_loader.load_flow_config(
        "config/flow_config/simple_test_flow.yaml"
    )

    if simple_flow:
        states_list = list(simple_flow.states.keys())

        print("状态名称 -> 旧方法% -> 新方法% -> 差异")
        print("-" * 55)

        async def compare_async():
            db_session = await db_manager.get_session()
            try:
                state_machine = StateMachine(simple_flow, db_session)

                for i, state in enumerate(states_list):
                    # 旧方法计算
                    old_progress = (i / len(simple_flow.states)) * 100

                    # 新方法计算
                    new_progress = state_machine.get_progress(state)

                    # 计算差异
                    diff = new_progress - old_progress

                    print(
                        f"{state:20} -> {old_progress:5.1f}% -> {new_progress:5.1f}% -> {diff:+5.1f}%"
                    )
            finally:
                await db_session.close()

        await compare_async()


async def main():
    """主测试函数"""
    print("🚀 进度计算测试套件")
    print("=" * 60)

    # 初始化数据库
    print("📊 初始化数据库...")
    await db_manager.init()

    try:
        # 测试进度计算
        await test_progress_calculation()

        # 分析路径结构
        test_path_analysis()

        # 比较新旧方法
        await compare_old_vs_new()

        print("\n✅ 测试完成!")
        print("\n💡 新进度计算方法的优势:")
        print("  1. 基于实际路径深度，而非简单的状态索引")
        print("  2. 考虑分支结构，更准确反映用户进展")
        print("  3. 最终状态自动设为100%")
        print("  4. 包含错误处理和回退机制")
        print("  5. 进度更平滑，避免跳跃式变化")
    finally:
        # 清理数据库连接
        await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main())
