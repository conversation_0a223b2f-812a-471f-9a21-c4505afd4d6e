import os
import tempfile
import json
from typing import Dict, Any, Optional
import aiosqlite
import httpx

from utils.app_config import settings


class StateMachineAPIClient:
    """状态机API客户端"""

    def __init__(self, base_url: str = settings.api.base_url):
        self.base_url = base_url
        self.session_id = None

    async def create_session(
        self, user_id: str = "demo_user", flow_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建新会话，可选择指定流程"""
        async with httpx.AsyncClient(timeout=600) as client:
            payload = {"user_id": user_id}
            if flow_name:
                payload["flow_name"] = flow_name

            print(f"🚀 创建会话 (流程: {flow_name or '默认'})...")
            response = await client.post(f"{self.base_url}/sessions", json=payload)
            result = response.json()
            if response.status_code == 200:
                self.session_id = result["session_id"]
                print(f"✅ 会话创建成功: {self.session_id}")
                print(f"   - 流程: {result['flow_name']}")
            else:
                print(f"❌ 会话创建失败: {result}")
            response.raise_for_status()
            return result

    async def get_current_state(self) -> Dict[str, Any]:
        """获取当前状态"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        async with httpx.AsyncClient(timeout=600) as client:
            response = await client.get(f"{self.base_url}/sessions/{self.session_id}")
            response.raise_for_status()
            result = response.json()
            print(f"📍 当前状态: {result['current_state']}")
            print(f"💬 提示: {result['state']['prompt']}")
            if result["state"].get("options"):
                print(f"🔘 选项: {', '.join(result['state']['options'])}")
            return result

    async def send_input(self, value: str) -> str:
        """发送用户输入"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        print(f"\n➡️  发送输入: '{value}'")
        full_response = ""
        async with httpx.AsyncClient(timeout=600) as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/v1/process_message",
                json={"text": value, "session_id": self.session_id},
            ) as response:
                response.raise_for_status()
                print("🤖 AI 回复: ", end="")
                async for chunk in response.aiter_text():
                    print(chunk, end="")
                    full_response += chunk
        print("\n")
        return full_response

    async def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        async with httpx.AsyncClient(timeout=600) as client:
            response = await client.get(f"{self.base_url}/sessions/{self.session_id}")
            response.raise_for_status()
            result = response.json()
            print(f"📊 会话信息:")
            print(f"  - 会话ID: {result['session_id']}")
            print(f"  - 用户ID: {result['user_id']}")
            print(f"  - 流程名称: {result['flow_name']}")
            print(f"  - 当前状态: {result['current_state']}")
            print(f"  - 会话状态: {result['status']}")
            return result

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        async with httpx.AsyncClient(timeout=600) as client:
            response = await client.get(f"{self.base_url}/health")
            response.raise_for_status()
            result = response.json()
            print(f"💚 服务健康状态: {result['status']}")
            return result

    def _get_mime_type(self, filename: str) -> str:
        """根据文件扩展名获取MIME类型"""
        extension = filename.lower().split(".")[-1]
        mime_types = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "pdf": "application/pdf",
            "doc": "application/msword",
            "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        }
        return mime_types.get(extension, "application/octet-stream")

    async def upload_file(self, file_path: str, filename: str) -> str:
        """上传文件"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        print(f"\n📤 上传文件: {filename}")
        mime_type = self._get_mime_type(filename)
        print(f"   - MIME类型: {mime_type}")

        async with httpx.AsyncClient(timeout=600) as client:
            with open(file_path, "rb") as f:
                files = {"file": (filename, f, mime_type)}
                response = await client.post(
                    f"{self.base_url}/sessions/{self.session_id}/files",
                    files=files,
                )
                response.raise_for_status()
                result = response.json()
                print("🤖 文件验证回复:")
                if result.get("file_upload_hint"):
                    print(f"   提示: {result['file_upload_hint']}")
                if result.get("validation_result"):
                    print(f"   验证结果: {result['validation_result']}")

                print("\n")
                return json.dumps(result, ensure_ascii=False, indent=2)

    async def create_test_files(self) -> Dict[str, str]:
        """创建测试文件"""
        test_files = {}

        # 创建身份证照片测试文件
        id_card_content = b"fake_id_card_image_content"
        id_card_path = os.path.join(tempfile.gettempdir(), "test_id_card.jpg")
        with open(id_card_path, "wb") as f:
            f.write(id_card_content)
        test_files["id_card"] = (id_card_path, "张三身份证.jpg")

        # 创建申请表格测试文件
        application_content = b"fake_application_form_content"
        application_path = os.path.join(tempfile.gettempdir(), "test_application.pdf")
        with open(application_path, "wb") as f:
            f.write(application_content)
        test_files["application"] = (application_path, "服务A申请表.pdf")

        print(f"📄 创建测试文件:")
        for key, (path, filename) in test_files.items():
            print(f"   - {filename}: {path}")

        return test_files

    async def cleanup_test_files(self, test_files: Dict[str, tuple]):
        """清理测试文件"""
        for key, (path, filename) in test_files.items():
            try:
                if os.path.exists(path):
                    os.remove(path)
                    print(f"🧹 清理测试文件: {filename}")
            except Exception as e:
                print(f"⚠️  清理文件失败 {filename}: {e}")


async def verify_session_in_db(session_id: str, expected_flow_name: str):
    """验证数据库中的会话数据"""
    print(f"\n🔍 正在验证数据库中 session_id: {session_id}...")
    # 从配置中获取数据库路径
    db_url = settings.get_database_url()
    db_path = db_url.split("///")[-1]
    try:
        async with aiosqlite.connect(f"{db_path}") as db:
            cursor = await db.execute(
                "SELECT flow_name FROM user_sessions WHERE session_id = ?",
                (session_id,),
            )
            row = await cursor.fetchone()
            await cursor.close()

        assert row is not None, f"数据库中未找到 session_id: {session_id}"
        actual_flow_name = row[0]
        assert actual_flow_name == expected_flow_name, (
            f"数据库中的 flow_name 不匹配. 预期: {expected_flow_name}, 实际: {actual_flow_name}"
        )
        print(f"✅ 数据库验证成功! flow_name 为 '{actual_flow_name}'.")
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        raise
