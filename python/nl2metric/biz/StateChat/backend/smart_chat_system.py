"""
完整的智能客服系统 - 集成提示模板和LLM处理
"""

import asyncio
import json
import re
import yaml
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from jinja2 import Template


class RealLLMClient:
    """真实的LLM客户端（需要配置API密钥）"""

    def __init__(self):
        # 在实际使用中需要配置这些环境变量
        self.api_key = os.getenv("OPENAI_API_KEY", "xxxxx")
        self.base_url = os.getenv("OPENAI_BASE_URL", "http://123.181.192.99:16701/v1")
        self.model = os.getenv("LLM_MODEL", "qwen3_32b")

        if not self.api_key:
            print("警告: 未配置OPENAI_API_KEY，将使用模拟模式")

    async def chat_completions_create(
        self, messages: List[Dict], temperature: float = 0.1
    ):
        """调用LLM API"""

        # 实际的API调用代码（需要openai库）
        import openai

        client = openai.AsyncOpenAI(api_key=self.api_key, base_url=self.base_url)
        return await client.chat.completions.create(
            model=self.model, messages=messages, temperature=temperature
        )

    def _extract_data_from_prompt(self, prompt: str) -> Dict[str, Any]:
        """从提示中提取数据"""
        result = {"missing_fields": [], "follow_up_message": ""}

        # 查找用户输入
        user_input_match = re.search(r"用户输入: (.+)", prompt)
        if user_input_match:
            user_text = user_input_match.group(1)

            # 提取邮箱
            email_match = re.search(
                r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}", user_text
            )
            if email_match:
                result["email"] = email_match.group()
            else:
                result["missing_fields"].append("email")

            # 提取手机号
            phone_match = re.search(r"1[3-9]\d{9}", user_text)
            if phone_match:
                result["phone"] = phone_match.group()
            else:
                result["missing_fields"].append("phone")

            # 提取姓名
            name_match = re.search(
                r"我叫(.+?)，|姓名[：:](.+?)，|姓名[：:](.+?)[。\s]", user_text
            )
            if name_match:
                result["name"] = (
                    name_match.group(1) or name_match.group(2) or name_match.group(3)
                )
            else:
                result["missing_fields"].append("name")

            # 生成追问
            if result["missing_fields"]:
                field_names = {"name": "姓名", "email": "邮箱", "phone": "手机号"}
                result["follow_up_message"] = (
                    f"请补充以下信息：{', '.join(field_names.get(f, f) for f in result['missing_fields'])}"
                )

        return result


class SmartChatState:
    """智能聊天状态"""

    def __init__(self, current_state: str):
        self.current_state = current_state
        self.messages = []
        self.user_data = {}
        self.is_complete = False
        self.context_info = {}


class SmartChatFlow:
    """智能聊天流程"""

    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.states = self.config["states"]
        self.prompts_dir = "prompts"
        self._load_prompt_templates()
        self.llm_client = RealLLMClient()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载流程配置"""
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    def _load_prompt_templates(self):
        """加载提示模板"""
        self.choice_template = self._load_template("llm_choice_template.jinja2")
        self.param_template = self._load_template(
            "parameter_extraction_template.jinja2"
        )

    def _load_template(self, filename: str) -> Template:
        """加载Jinja2模板"""
        path = os.path.join(self.prompts_dir, filename)
        with open(path, "r", encoding="utf-8") as f:
            return Template(f.read())

    async def process_message(
        self, state: SmartChatState, message: str
    ) -> SmartChatState:
        """处理用户消息"""
        state.messages.append({"role": "user", "content": message})

        current_config = self.states.get(state.current_state)
        if not current_config:
            state.messages.append({"role": "assistant", "content": "错误：无效的状态"})
            return state

        # 根据输入类型处理
        if current_config["input_type"] == "choice":
            next_state = await self._process_choice_with_llm(message, current_config)
            if not next_state:
                options = current_config.get("options", [])
                state.messages.append(
                    {
                        "role": "assistant",
                        "content": f"请选择有效选项：{', '.join(options)}",
                    }
                )
                return state
        elif current_config["input_type"] == "text":
            next_state = await self._process_text_with_llm(
                state, message, current_config
            )
        else:
            next_state = current_config.get("next")

        # 更新状态
        if next_state:
            state.current_state = next_state
            next_config = self.states.get(next_state)

            if next_config:
                state.messages.append(
                    {"role": "assistant", "content": next_config["prompt"]}
                )

                if next_state in self.config.get("final_states", []):
                    state.is_complete = True

        return state

    async def _process_choice_with_llm(
        self, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """使用LLM处理选择"""
        options = config.get("options", [])
        next_mapping = config.get("next", {})
        prompt_hint = config.get("llm_prompt_hint", "")

        # 构建提示
        prompt = self.choice_template.render(
            prompt_hint=prompt_hint, user_input=message, options=options
        )

        try:
            response = await self.llm_client.chat_completions_create(
                messages=[{"role": "user", "content": prompt}], temperature=0.1
            )

            choice = response.choices[0].message.content.strip()

            # 匹配选项
            for option in options:
                if option.lower() in choice.lower() or choice.lower() in option.lower():
                    return next_mapping.get(option)

        except Exception as e:
            print(f"LLM调用失败: {e}")

        return None

    async def _process_text_with_llm(
        self, state: SmartChatState, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """使用LLM处理文本"""
        next_state = config.get("next")

        if "requires" in config:
            required_fields = config["requires"]
            prompt_hint = config.get("llm_prompt_hint", "")

            # 使用LLM提取数据
            extracted_data = await self._extract_fields_with_llm(
                message, required_fields, prompt_hint, state.context_info
            )

            if extracted_data.get("missing_fields"):
                # 生成追问
                follow_up = extracted_data.get("follow_up_message", "")
                state.messages.append({"role": "assistant", "content": follow_up})

                # 保存已提取的数据
                for field in required_fields:
                    if field in extracted_data and extracted_data[field]:
                        state.context_info[field] = extracted_data[field]
                        state.user_data[field] = extracted_data[field]

                return None
            else:
                # 保存所有数据
                for field in required_fields:
                    if field in extracted_data:
                        state.user_data[field] = extracted_data[field]
                        state.context_info[field] = extracted_data[field]

        return next_state

    async def _extract_fields_with_llm(
        self,
        text: str,
        required_fields: List[str],
        prompt_hint: str,
        context_info: Dict[str, Any],
    ) -> Dict[str, Any]:
        """使用LLM提取字段"""

        # 构建格式说明
        format_example = {
            "name": "张三",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "missing_fields": [],
            "follow_up_message": "",
        }

        # 构建提示
        prompt = self.param_template.render(
            context_info=json.dumps(context_info, ensure_ascii=False),
            prompt_hint=prompt_hint,
            required_fields=required_fields,
            user_input=text,
            format_instructions=json.dumps(format_example, ensure_ascii=False),
        )

        try:
            response = await self.llm_client.chat_completions_create(
                messages=[{"role": "user", "content": prompt}], temperature=0.1
            )

            result_text = response.choices[0].message.content.strip()

            # 解析JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]

            return json.loads(result_text)

        except Exception as e:
            print(f"LLM提取失败: {e}")
            return {
                "missing_fields": required_fields,
                "follow_up_message": "请提供所需信息",
            }


class SmartChatManager:
    """智能聊天管理器"""

    def __init__(self, config_path: str):
        self.flow = SmartChatFlow(config_path)
        self.sessions: Dict[str, SmartChatState] = {}

    async def handle_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        if session_id not in self.sessions:
            self.sessions[session_id] = SmartChatState(
                current_state=self.flow.config["initial_state"]
            )

            initial_config = self.flow.states[self.flow.config["initial_state"]]
            self.sessions[session_id].messages.append(
                {"role": "assistant", "content": initial_config["prompt"]}
            )

        state = self.sessions[session_id]
        new_state = await self.flow.process_message(state, message)
        self.sessions[session_id] = new_state

        latest_message = new_state.messages[-1] if new_state.messages else {}

        return {
            "response": latest_message.get("content", ""),
            "current_state": new_state.current_state,
            "user_data": new_state.user_data,
            "is_complete": new_state.is_complete,
            "messages": new_state.messages,
        }

    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        if session_id in self.sessions:
            state = self.sessions[session_id]
            return {
                "current_state": state.current_state,
                "user_data": state.user_data,
                "message_count": len(state.messages),
                "is_complete": state.is_complete,
            }
        return {}


async def demo_smart_chat():
    """演示智能聊天系统"""
    print("=== 智能客服系统演示 ===\n")
    print("系统已集成提示模板，支持LLM驱动的分支选择和参数提取\n")

    manager = SmartChatManager("config/flow_config/simple_test_flow.yaml")
    session_id = f"smart_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # 完整对话流程
    conversation = [
        ("你好", "欢迎用户"),
        ("还没有注册", "选择未注册"),
        ("注册个人账号", "选择个人注册类型"),
        ("我叫李华，邮箱是*****************", "提供部分信息"),
        ("手机号是13900001111", "补充完整信息"),
        ("我想办理服务A", "选择服务"),
    ]

    for i, (message, desc) in enumerate(conversation, 1):
        print(f"【第{i}轮 - {desc}】")
        print(f"用户：{message}")

        result = await manager.handle_message(session_id, message)

        print(f"系统：{result['response']}")
        print(f"状态：{result['current_state']}")

        if result["user_data"]:
            print(f"已收集：{json.dumps(result['user_data'], ensure_ascii=False)}")

        print("-" * 60)
        await asyncio.sleep(0.5)

    # 总结
    session_info = manager.get_session_info(session_id)
    print("\n=== 会话总结 ===")
    print(f"最终状态：{session_info['current_state']}")
    print(f"用户数据：{json.dumps(session_info['user_data'], ensure_ascii=False)}")
    print(f"消息数：{session_info['message_count']}")
    print(f"是否完成：{session_info['is_complete']}")


if __name__ == "__main__":
    asyncio.run(demo_smart_chat())
