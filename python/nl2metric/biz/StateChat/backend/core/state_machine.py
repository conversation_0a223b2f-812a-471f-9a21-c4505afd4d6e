"""
状态机核心引擎实现
"""

import uuid
from datetime import datetime
from types import MethodType
from typing import Dict, List, Optional, Union

from loguru import logger
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from transitions import Machine

from api.schemas import ProcessInputResponse
from models.context import FlowContext
from models.session import UserSession, SessionStatus, SessionSummary
from models.state import FlowConfig, StateNode, UserInput, InputType, StateTransition
from storage.session_store import SessionStore, ContextStore
from utils.ai_engine import default_ai_engine
from .exceptions import StateNotFoundError, SessionNotFoundError, FlowConfigError
from .intent import IntentType


class StateMachine:
    """状态机核心引擎"""

    def __init__(
        self,
        flow_config: FlowConfig,
        db_session: AsyncSession,
    ):
        """
        初始化状态机

        Args:
            flow_config: 流程配置
            db_session: 数据库会话
        """
        self.flow_config = flow_config
        # 验证流程配置
        self._validate_flow_config()
        self.db_session = db_session

        # 初始化存储管理器
        self.session_store = SessionStore(db_session)
        self.context_store = ContextStore(db_session)

        self.state_map: Dict[str, StateNode] = flow_config.states
        self.states = list(self.state_map.keys())
        self.transitions: List[dict] = []
        self.current_session: Optional[UserSession] = None
        self.current_context: Optional[FlowContext] = None
        self.current_input: Optional[UserInput] = None

        for stage in flow_config.states.values():
            if isinstance(stage.next, dict):  # conditional branching
                for src, dest in stage.next.items():
                    self.transitions.append(
                        {
                            "trigger": f"advance_from_{stage.name}_to_{dest}",
                            "source": stage.name,
                            "dest": dest,
                            "conditions": [f"can_{stage.name}_to_{dest}"],
                        }
                    )
                    setattr(
                        self,
                        f"can_{stage.name}_to_{dest}",
                        MethodType(lambda _: True, self),
                    )

            elif isinstance(stage.next, str):  # linear flow
                self.transitions.append(
                    {
                        "trigger": f"advance_{stage.next}",
                        "source": stage.name,
                        "dest": stage.next,
                        "conditions": [f"can_{stage.name}_to_{stage.next}"],
                    }
                )
                setattr(
                    self,
                    f"can_{stage.name}_to_{stage.next}",
                    MethodType(lambda _: True, self),
                )
            elif stage.next is None:  # no next state
                self.transitions.append(
                    {
                        "trigger": f"advance_{stage.name}",
                        "source": "*",
                        "dest": stage.name,
                        "conditions": [f"can_{stage.name}"],
                    }
                )
                setattr(
                    self,
                    f"can_{stage.name}",
                    MethodType(lambda _: True, self),
                )
        self.m = Machine(
            model=self,
            states=self.states,
            transitions=self.transitions,
            initial=flow_config.initial_state,
            auto_transitions=False,
        )

        logger.info(f"状态机初始化完成: {flow_config.flow_name} v{flow_config.version}")

    async def _process_llm_choice_input(
        self, state: StateNode, user_input: UserInput, context: FlowContext
    ) -> str:
        """
        处理LLM相关的选择逻辑。
        更新user_input.value，并返回用于状态转移的next_state_key。
        """
        logger.info(
            f"调用LLM进行选择: 状态={state.name}, LLM提示={state.llm_prompt_hint}, 选项={state.options}, 用户输入={user_input.value}"
        )
        llm_chosen_value = await default_ai_engine.get_llm_choice(
            prompt_hint=state.llm_prompt_hint,
            options=state.options,
            user_input=user_input.value,
            context=context,
        )
        logger.info(f"LLM选择结果: {llm_chosen_value}")
        user_input.value = llm_chosen_value  # 更新user_input.value为LLM的选择结果
        return llm_chosen_value  # LLM选择结果直接作为next_state_key

    async def _process_llm_parameter_extraction_save_to_context(
        self, state: StateNode, user_input: UserInput, context: FlowContext
    ):
        """
        处理LLM相关的参数提取逻辑。
        更新context。
        返回是否需要追问（如果有缺失字段）
        """
        logger.info(
            f"调用LLM提取参数: 状态={state.name}, LLM提示={state.llm_prompt_hint}, 必需字段={state.requires}, 用户输入={user_input.value}"
        )
        extracted_params = await default_ai_engine.extract_data(
            prompt_hint=state.llm_prompt_hint,
            user_input=user_input.value,
            required_fields=state.requires,
            context=context,
        )
        logger.info(f"LLM提取参数结果: {extracted_params}")

        # 检查是否有缺失字段
        missing_fields = extracted_params.get("missing_fields", [])
        followup_message = extracted_params.get("followup_message", "")

        # 保存提取的参数到业务信息
        for key, value in extracted_params.items():
            if key in ["missing_fields", "followup_message"]:
                continue  # 跳过控制字段

            if value is not None and value != "":
                if (
                    "personal" in state.name
                    or "owner" in state.name
                    or "director" in state.name
                ):
                    context.update_personal_info(key, value)
                elif "business_info" in state.name or "company" in state.name:
                    context.update_business_info(key, value)
                elif "license" in state.name or "permit" in state.name:
                    context.update_license_info(key, value)

                # 将提取的参数也添加到form_data中
                context.add_form_data(key, value, "extracted_param", True)
            else:
                logger.warning(f"提取的参数 '{key}' 为空，跳过添加到业务信息")

        await self.context_store.update_context(context)

        # 返回追问信息
        return {
            "has_missing_fields": len(missing_fields) > 0,
            "missing_fields": missing_fields,
            "followup_message": followup_message,
        }

    def _validate_flow_config(self):
        """验证流程配置的完整性"""
        # 检查初始状态是否存在
        if self.flow_config.initial_state not in self.flow_config.states:
            raise FlowConfigError(f"初始状态 '{self.flow_config.initial_state}' 不存在")

        # 检查终止状态是否存在
        for final_state in self.flow_config.final_states:
            if final_state not in self.flow_config.states:
                raise FlowConfigError(f"终止状态 '{final_state}' 不存在")

        # 检查状态转移的目标状态是否存在
        for state_name, state in self.flow_config.states.items():
            if state.next:
                if isinstance(state.next, str):
                    if state.next not in self.flow_config.states:
                        raise FlowConfigError(
                            f"状态 '{state_name}' 的目标状态 '{state.next}' 不存在"
                        )
                elif isinstance(state.next, dict):
                    for condition, target_state in state.next.items():
                        if target_state and target_state not in self.flow_config.states:
                            raise FlowConfigError(
                                f"状态 '{state_name}' 的条件 '{condition}' 对应的目标状态 '{target_state}' 不存在"
                            )

    async def create_session(
        self, user_id: Optional[str] = None, session_id: Optional[str] = None
    ) -> UserSession:
        """
        创建新的用户会话

        Args:
            user_id: 用户ID，可选
            session_id: 会话ID，可选，不提供则自动生成

        Returns:
            UserSession: 新创建的会话对象
        """
        if not session_id:
            session_id = str(uuid.uuid4())

        # 创建会话
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            flow_name=self.flow_config.flow_name,
            current_state=self.flow_config.initial_state,
            status=SessionStatus.ACTIVE,
        )

        # 创建上下文
        context = FlowContext(
            session_id=session_id, flow_name=self.flow_config.flow_name
        )

        # 存储会话和上下文 (根据是否使用数据库)

        await self.session_store.create_session(session)
        await self.context_store.create_context(context)

        logger.info(
            f"创建新会话: {session_id}, 用户: {user_id}, 初始状态: {self.flow_config.initial_state}"
        )

        return session

    async def get_session(self, session_id: str) -> UserSession:
        """获取会话"""
        current_session = await self.session_store.get_session(session_id)
        if not current_session:
            raise SessionNotFoundError(session_id)
        return current_session

    async def get_context(self, session_id: str) -> FlowContext:
        """获取会话上下文"""
        current_contexts = await self.context_store.get_context(session_id)
        if not current_contexts:
            raise SessionNotFoundError(session_id)
        return current_contexts

    async def get_current_state(self, session_id: str) -> StateNode:
        """获取当前状态节点"""
        session = await self.get_session(session_id)
        current_state_name = session.current_state
        if current_state_name not in self.flow_config.states:
            raise StateNotFoundError(current_state_name)
        return self.flow_config.states[current_state_name]

    async def add_transition(self, session_id: str, transition: StateTransition):
        await self.session_store.add_transition(session_id, transition)
        self.current_session.add_transition(transition)

    async def process_user_input(
        self, session_id: str, user_input: UserInput
    ) -> ProcessInputResponse:
        """
        处理用户输入并执行状态转移
        Args:
            session_id: 会话ID
            user_input: 用户输入
        Returns:
            Dict: 处理结果，包含下一状态信息
        """
        try:
            self.current_input = user_input
            logger.info(f"处理用户输入: {user_input}")
            self.current_session: UserSession = await self.get_session(session_id)
            self.current_context: FlowContext = await self.get_context(session_id)
            current_state = await self.get_current_state(session_id)
            self.m.set_state(current_state.name)

            logger.info(
                f"处理用户输入: 会话={session_id}, 当前状态={current_state.name}, 输入类型={user_input.input_type}"
            )

            next_state_key = None
            parameter_extraction_done = False  # Flag to check if extraction was done
            if current_state.requires:
                extraction_result = (
                    await self._process_llm_parameter_extraction_save_to_context(
                        current_state, user_input, self.current_context
                    )
                )
                parameter_extraction_done = True  # Set flag

                # 如果有缺失字段，返回追问消息而不是继续状态转移
                if extraction_result.get("has_missing_fields", False):
                    missing_fields = extraction_result.get("missing_fields", [])
                    followup_message = extraction_result.get("followup_message", "")

                    logger.info(f"检测到缺失字段 {missing_fields}，生成追问消息")

                    # 保存当前状态信息到上下文，用于后续处理
                    self.current_context.add_form_data(
                        "pending_extraction",
                        {
                            "missing_fields": missing_fields,
                            "state_name": current_state.name,
                            "requires": current_state.requires,
                        },
                        "pending_extraction",
                    )
                    await self.context_store.update_context(self.current_context)

                    return ProcessInputResponse(
                        success=True,
                        partial_success=True,
                        missing_fields=missing_fields,
                        followup_required=True,
                        message=followup_message,
                        current_state=current_state,
                        intent_type=IntentType.BUSINESS,
                    )
                else:
                    # 所有字段已收集完成，跳过常规验证，直接进行状态转移
                    logger.info("所有必填字段已收集完成，跳过验证直接进行状态转移")
            if (
                current_state.input_type == InputType.CHOICE
                and len(current_state.next) > 1
            ):
                next_state_key = await self._process_llm_choice_input(
                    current_state, user_input, self.current_context
                )

            if not self.validate_user_input(
                current_state, user_input, self.current_context
            ):
                return ProcessInputResponse(
                    success=False,
                    error="输入处理失败",
                    current_state=current_state,
                )
            # Only update context with raw input if parameter extraction didn't happen
            if not parameter_extraction_done:
                await self._update_context_with_input(
                    self.current_context, current_state, user_input
                )

            # 验证用户输入 (在LLM处理和上下文更新之后)
            # 确定状态机触发器
            trigger = None
            if isinstance(current_state.next, str):
                trigger = f"advance_{current_state.next}"
            elif isinstance(current_state.next, dict):
                if next_state_key in current_state.next:
                    next_state = current_state.next[next_state_key]
                    trigger = f"advance_from_{current_state.name}_to_{next_state}"
                else:
                    # 如果LLM选择的键不在next中，尝试使用"none"作为回退
                    if "none" in current_state.next:
                        next_state = current_state.next["none"]
                        trigger = f"advance_from_{current_state.name}_to_{next_state}"
                    else:
                        raise ValueError(
                            f"无效的输入 '{next_state_key}'，在状态 '{current_state.name}' 中没有对应的转移"
                        )
            else:
                # 线性流程或最终状态
                trigger = f"advance_{current_state.next}"

            if not trigger or not hasattr(self, trigger):
                raise ValueError(
                    f"Invalid input '{user_input}' for state '{current_state.name}'"
                )
            logger.info(f"trigger before state : {self.state}")  # noqa
            before_state = self.state  # noqa
            getattr(self, trigger)()  # noqa
            new_current_state_name: str = self.state  # noqa
            self.current_session.current_state = new_current_state_name
            logger.info(f"trigger after state : {new_current_state_name}")
            transition = StateTransition(
                from_state=before_state,
                to_state=self.state,  # noqa
                user_input=user_input.model_dump(),
            )
            await self.add_transition(session_id, transition)

            # 获取新状态信息
            new_current_state: StateNode = self.flow_config.states[
                new_current_state_name
            ]

            if new_current_state_name in self.flow_config.final_states:
                self.current_session.status = SessionStatus.COMPLETED
                logger.info(f"会话完成: {session_id}")
                await self.session_store.update_session(self.current_session)
                await self.context_store.update_context(self.current_context)
                return ProcessInputResponse(
                    success=True,
                    completed=True,
                    processed_state=self.state_map[before_state],
                    current_state=new_current_state,
                    progress=100,
                    intent_type=IntentType.BUSINESS,
                    message="整个流程已完成。",
                )
            await self.session_store.update_session(self.current_session)
            await self.context_store.update_context(self.current_context)

            logger.info(f"状态转移成功: {before_state} -> {new_current_state_name}")
            next_states = []
            # 可能的下一个状态
            possible_states: Optional[Union[str, Dict[str, str]]] = self.state_map[
                new_current_state_name
            ].next
            if isinstance(possible_states, dict):
                for key, value in possible_states.items():
                    next_states.append(self.state_map[value])
            elif isinstance(possible_states, str):
                next_states.append(self.state_map[possible_states])
            return ProcessInputResponse(
                success=True,
                completed=False,
                processed_state=self.state_map[before_state],
                current_state=new_current_state,
                next_states=next_states,
                progress=self.get_progress(new_current_state_name),
            )

        except Exception as e:
            logger.exception(f"处理用户输入时发生意外错误: {e}")
            try:
                session = await self.get_session(session_id)
                if session:
                    session.status = SessionStatus.ERROR
                    await self.session_store.update_session(session)
                    current_state_name = session.current_state
                    current_state_node = self.state_map.get(current_state_name)
                else:
                    current_state_node = None

                return ProcessInputResponse(
                    success=False,
                    error=f"处理用户输入时发生意外错误: {e}",
                    current_state=current_state_node,
                )
            except Exception as inner_e:
                logger.exception(f"在错误处理中发生更严重的错误: {inner_e}")
                # 返回一个标准的错误响应，避免返回字典
                return ProcessInputResponse(
                    success=False,
                    error=f"在错误处理中发生更严重的错误: {inner_e}",
                    current_state=None,
                )

    async def _update_context_with_input(
        self, context: FlowContext, state: StateNode, user_input: UserInput
    ):
        """
        根据用户输入更新上下文。
        此方法现在主要负责将用户输入（可能已被LLM处理过）保存到form_data。
        文件上传和LLM参数提取及业务信息更新已移至_process_llm_input或由外部接口处理。
        """
        # 无论输入类型如何，首先将原始用户输入保存到上下文的 form_data 中
        input_type_str = (
            state.input_type.value
            if hasattr(state.input_type, "value")
            else state.input_type
        )
        context.add_form_data(state.name, user_input.value, input_type_str)

        # 更新数据库中的上下文
        await self.context_store.update_context(context)

    async def _calculate_progress(self, session_id: str) -> float:
        """计算流程进度"""
        # 获取会话以使用新的进度计算方法
        session = await self.get_session(session_id)
        self.current_session = session
        return self.get_progress(session.current_state)

    async def get_session_summary(self, session_id: str) -> SessionSummary:
        """获取会话摘要"""
        session = await self.get_session(session_id)
        progress = await self._calculate_progress(session_id)

        return SessionSummary(
            session_id=session.session_id,
            user_id=session.user_id,
            flow_name=session.flow_name,
            current_state=session.current_state,
            status=session.status,
            progress_percentage=progress,
            created_at=session.created_at,
            updated_at=session.updated_at,
        )

    async def list_sessions(
        self, user_id: Optional[str] = None
    ) -> List[SessionSummary]:
        """列出会话摘要"""
        if self.db_session:
            session_summaries = await self.session_store.list_sessions(user_id)
            # 对于从数据库加载的会话，需要单独计算进度
            for summary in session_summaries:
                summary.progress_percentage = await self._calculate_progress(
                    summary.session_id
                )
            return session_summaries
        else:
            # 如果没有db_session，则无法列出会话，因为会话不再存储在内存中
            # 应该始终使用db_session，这里可以抛出错误或返回空列表
            logger.warning("未提供数据库会话，无法列出会话。")
            return []

    async def pause_session(self, session_id: str):
        """暂停会话"""
        session = await self.get_session(session_id)
        session.status = SessionStatus.PAUSED
        session.updated_at = datetime.now()
        logger.info(f"会话已暂停: {session_id}")

        await self.session_store.update_session(session)

    async def resume_session(self, session_id: str):
        """恢复会话"""
        session = await self.get_session(session_id)
        if session.status == SessionStatus.PAUSED:
            session.status = SessionStatus.ACTIVE
            session.updated_at = datetime.now()
            logger.info(f"会话已恢复: {session_id}")
            await self.session_store.update_session(session)

    async def delete_session(self, session_id: str):
        """删除会话"""
        await self.session_store.delete_session(session_id)
        logger.info(f"会话已删除: {session_id}")

    def get_progress(self, new_current_state_name: str) -> float:
        """
        获取会话进度百分比
        基于实际访问路径和状态权重计算
        """
        try:
            # 如果是最终状态，返回100%
            if new_current_state_name in self.flow_config.final_states:
                return 100.0

            # 如果没有当前会话或转换历史，使用简化计算
            if not self.current_session or not self.current_session.history:
                return self._get_simple_progress(new_current_state_name)

            # 计算已完成状态的权重和
            completed_weight = self._calculate_completed_weight()

            # 估算剩余状态的权重
            estimated_remaining_weight = self._estimate_remaining_weight(
                new_current_state_name
            )

            # 计算总权重
            total_weight = completed_weight + estimated_remaining_weight

            if total_weight > 0:
                progress = (completed_weight / total_weight) * 100
                # 确保不超过95%（为最终状态保留5%）
                return min(progress, 95.0)
            else:
                return self._get_simple_progress(new_current_state_name)

        except Exception as e:
            logger.warning(f"计算进度时出错: {e}, 使用简单计算方法")
            return self._get_simple_progress(new_current_state_name)

    # 状态权重配置
    STATE_WEIGHTS = {
        InputType.TEXT: 2.0,  # 文本输入需要更多用户努力
        InputType.CHOICE: 1.5,  # 选择相对简单
        InputType.FILE: 2.5,  # 文件上传最复杂
        "display": 0.5,  # 显示信息状态
        "processing": 1.0,  # 处理状态
    }

    def _get_state_weight(self, state_name: str) -> float:
        """获取状态权重"""
        state = self.flow_config.states.get(state_name)
        if not state:
            return 1.0

        # 根据输入类型确定基础权重
        if hasattr(state, "input_type") and state.input_type:
            base_weight = self.STATE_WEIGHTS.get(state.input_type, 1.0)
        else:
            base_weight = 1.0

        # 如果是最终状态，给予更高权重
        if state_name in self.flow_config.final_states:
            base_weight *= 1.5

        # 如果有必填字段，增加权重
        if hasattr(state, "requires") and state.requires:
            base_weight *= 1 + 0.2 * len(state.requires)

        return base_weight

    def _get_simple_progress(self, state_name: str) -> float:
        """简化的进度计算方法"""
        try:
            # 使用BFS计算最短路径深度
            current_depth = self._calculate_state_depth(state_name)
            max_depth = self._estimate_max_depth()

            if max_depth > 0:
                progress = (current_depth / max_depth) * 90  # 简化计算最多到90%
                return min(progress, 90.0)
            else:
                state_names = list(self.flow_config.states.keys())
                if state_name in state_names:
                    return min(
                        (state_names.index(state_name) / len(state_names)) * 90,
                        90.0,
                    )
                return 0.0
        except:
            return 0.0

    def _calculate_completed_weight(self) -> float:
        """计算已完成状态的总权重"""
        if not self.current_session or not self.current_session.history:
            return 0.0

        completed_weight = 0.0
        visited_states = set()

        # 遍历所有转换，计算访问过的状态权重
        for transition in self.current_session.history:
            from_state = transition.from_state
            if from_state and from_state not in visited_states:
                visited_states.add(from_state)
                completed_weight += self._get_state_weight(from_state)

        # 添加当前状态的权重
        if self.current_session.current_state:
            current_state = self.current_session.current_state
            if current_state not in visited_states:
                completed_weight += self._get_state_weight(current_state)

        return completed_weight

    def _estimate_remaining_weight(self, current_state: str) -> float:
        """估算剩余状态的权重"""
        # 使用启发式方法估算剩余路径

        # 1. 如果当前状态接近终点，剩余权重较小
        if current_state in self.flow_config.final_states:
            return 0.0

        # 2. 检查从当前状态到终点的最短路径
        min_remaining_weight = float("inf")

        for final_state in self.flow_config.final_states:
            try:
                path_weight = self._calculate_path_weight(current_state, final_state)
                if path_weight < min_remaining_weight:
                    min_remaining_weight = path_weight
            except:
                continue

        # 3. 如果找不到路径，使用默认估算
        if min_remaining_weight == float("inf"):
            # 估算平均每个状态权重为1.5，剩余约3-5个状态
            min_remaining_weight = 4.5

        return min_remaining_weight

    def _calculate_path_weight(self, from_state: str, to_state: str) -> float:
        """计算两个状态之间路径的权重"""
        if from_state == to_state:
            return 0.0

        # 使用BFS找到最短路径并计算权重
        from collections import deque

        queue = deque([(from_state, 0.0)])  # (state, accumulated_weight)
        visited = {from_state}

        while queue:
            current_state, accumulated_weight = queue.popleft()

            # 获取当前状态的下一个状态
            current_node = self.flow_config.states.get(current_state)
            if not current_node or not current_node.next:
                continue

            next_states = []
            if isinstance(current_node.next, str):
                next_states = [
                    (current_node.next, self._get_state_weight(current_node.next))
                ]
            elif isinstance(current_node.next, dict):
                for next_state_name in current_node.next.values():
                    if next_state_name:
                        next_states.append(
                            (next_state_name, self._get_state_weight(next_state_name))
                        )

            for next_state, state_weight in next_states:
                if next_state == to_state:
                    return accumulated_weight + state_weight

                if next_state not in visited:
                    visited.add(next_state)
                    queue.append((next_state, accumulated_weight + state_weight))

        # 如果找不到路径，返回一个较大的值
        return float("inf")

    def _estimate_max_depth(self) -> int:
        """估算流程的最大深度"""
        max_depth = 0

        # 计算到每个最终状态的深度
        for final_state in self.flow_config.final_states:
            depth = self._calculate_state_depth(final_state)
            if depth > max_depth:
                max_depth = depth

        # 如果没有找到有效深度，使用状态数量的一半
        return max(max_depth, len(self.flow_config.states) // 2)

    async def get_detailed_progress(self, session_id: str) -> dict:
        """获取详细的进度信息"""
        try:
            session = await self.get_session(session_id)
            self.current_session = session

            current_state = session.current_state
            progress = self.get_progress(current_state)

            # 计算各种指标
            completed_states = len(session.history) if session.history else 0
            estimated_remaining = self._estimate_remaining_states(current_state)

            # 计算分支复杂度
            branch_complexity = self._calculate_branch_complexity(current_state)

            return {
                "overall_progress": round(progress, 2),
                "completed_states": completed_states,
                "estimated_remaining_states": estimated_remaining,
                "current_state": current_state,
                "branch_complexity": branch_complexity,
                "is_near_completion": progress >= 80,
                "time_efficiency": self._calculate_time_efficiency(session),
            }
        except Exception as e:
            logger.error(f"获取详细进度信息失败: {e}")
            return {
                "overall_progress": 0.0,
                "completed_states": 0,
                "estimated_remaining_states": 0,
                "current_state": "unknown",
                "branch_complexity": "low",
                "is_near_completion": False,
                "time_efficiency": 1.0,
            }

    def _estimate_remaining_states(self, current_state: str) -> int:
        """估算剩余状态数量"""
        if current_state in self.flow_config.final_states:
            return 0

        # 简化估算：基于到最近终点的距离
        min_remaining = float("inf")

        for final_state in self.flow_config.final_states:
            try:
                depth = self._calculate_state_depth(
                    final_state
                ) - self._calculate_state_depth(current_state)
                if depth > 0 and depth < min_remaining:
                    min_remaining = depth
            except:
                continue

        # 如果找不到有效估算，返回默认值
        if min_remaining == float("inf"):
            return 3  # 默认估算还有3个状态

        return max(1, min_remaining)

    def _calculate_branch_complexity(self, current_state: str) -> str:
        """计算当前分支的复杂度"""
        state = self.flow_config.states.get(current_state)
        if not state:
            return "low"

        # 根据下一个状态的数量判断复杂度
        if isinstance(state.next, dict):
            branch_count = len([v for v in state.next.values() if v])
            if branch_count > 3:
                return "high"
            elif branch_count > 1:
                return "medium"

        return "low"

    def _calculate_time_efficiency(self, session: UserSession) -> float:
        """计算时间效率（已完成状态数/已用时间（分钟））"""
        if not session.created_at:
            return 1.0

        time_diff = datetime.now() - session.created_at
        minutes_spent = time_diff.total_seconds() / 60

        if minutes_spent < 1:
            return 1.0

        completed_states = len(session.history) if session.history else 1
        return round(completed_states / minutes_spent, 2)

    def _calculate_state_depth(self, target_state: str) -> int:
        """
        计算从初始状态到目标状态的最短路径深度
        使用BFS算法找到最短路径
        """
        if target_state == self.flow_config.initial_state:
            return 0

        from collections import deque

        # BFS队列：(当前状态, 深度)
        queue = deque([(self.flow_config.initial_state, 0)])
        visited = {self.flow_config.initial_state}

        while queue:
            current_state, depth = queue.popleft()

            # 获取当前状态的下一个状态
            current_node = self.flow_config.states.get(current_state)
            if not current_node or not current_node.next:
                continue

            next_states = []
            if isinstance(current_node.next, str):
                next_states = [current_node.next]
            elif isinstance(current_node.next, dict):
                next_states = list(current_node.next.values())

            for next_state in next_states:
                if next_state == target_state:
                    return depth + 1

                if next_state not in visited:
                    visited.add(next_state)
                    queue.append((next_state, depth + 1))

        # 如果找不到路径，返回一个基于状态在配置中位置的估算值
        state_names = list(self.flow_config.states.keys())
        if target_state in state_names:
            return state_names.index(target_state)

        return 0

    def _estimate_total_flow_depth(self) -> int:
        """
        估算整个流程的平均深度
        计算从初始状态到所有最终状态的平均路径长度
        """
        if not self.flow_config.final_states:
            return len(self.flow_config.states)

        total_depth = 0
        valid_paths = 0

        for final_state in self.flow_config.final_states:
            depth = self._calculate_state_depth(final_state)
            if depth > 0:
                total_depth += depth
                valid_paths += 1

        if valid_paths > 0:
            return total_depth // valid_paths
        else:
            # 回退到状态总数的估算
            return max(len(self.flow_config.states) // 2, 1)

    def _validate_input_type(self, state: StateNode, user_input: UserInput) -> bool:
        """验证输入类型匹配"""
        state_input_type = (
            state.input_type.value
            if hasattr(state.input_type, "value")
            else state.input_type
        )

        if state_input_type == InputType.TEXT.value and not isinstance(
            user_input.value, str
        ):
            raise ValueError(
                f"期望类型 {state_input_type}, 实际类型 {type(user_input.value).__name__}"
            )
        elif state_input_type == InputType.CHOICE.value and not isinstance(
            user_input.value, str
        ):
            raise ValueError(
                f"期望类型 {state_input_type} 对应的字符串, 实际类型 {type(user_input.value).__name__}"
            )
        return True

    def _validate_required_fields(self, state: StateNode, context: FlowContext) -> bool:
        """验证必填字段"""
        if not state.requires:
            return True

        for field in state.requires:
            if (
                not context.get_form_value(field)
                and not context.business_info.get(field)
                and not context.personal_info.get(field)
                and not context.license_info.get(field)
            ):
                raise ValueError(f"缺少必填字段: {field}")
        return True

    def _validate_choices(self, state: StateNode, user_input: UserInput) -> bool:
        """验证选择项"""
        if state.input_type == InputType.CHOICE and state.options:
            chosen_value = user_input.value.strip()

            if chosen_value not in state.options:
                raise ValueError(
                    f"无效选择 '{chosen_value}', 可选项: {', '.join(state.options)}"
                )
        return True

    def validate_user_input(
        self, state: StateNode, user_input: UserInput, context: FlowContext
    ) -> bool:
        """验证用户输入"""
        try:
            if not self._validate_input_type(state, user_input):
                return False

            if not self._validate_required_fields(state, context):
                return False

            if not self._validate_choices(state, user_input):
                return False
            return True

        except Exception as e:
            logger.exception(f"输入验证失败: {e}")
            return False
