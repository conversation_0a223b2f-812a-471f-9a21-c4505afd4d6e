"""
文件验证引擎实现
"""

import os
import re
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import aiofiles
from loguru import logger

from models.context import FlowContext, FileUpload
from models.file_validation import (
    FileValidationConfig,
    FileValidationRule,
    NamingConvention,
    ValidationResult,
    FileValidationError,
    FileValidationSummary,
    ValidationStatus,
    ValidationType,
    MultimodalValidationResult,
    MultimodalValidationConfig,
)
from models.state import StateNode
from utils.ai_engine import default_ai_engine


class FileValidator:
    """文件验证引擎"""

    def __init__(self):
        """初始化文件验证器"""
        self.supported_image_types = [
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/webp",
            "image/bmp",
            "image/tiff",
        ]
        self.supported_document_types = [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ]
        self.max_file_size_default = 10 * 1024 * 1024  # 10MB

    async def validate_files(
        self, session_id: str, state: StateNode, context: FlowContext
    ) -> FileValidationSummary:
        """
        验证指定状态下的所有文件

        Args:
            session_id: 会话ID
            state: 当前状态节点
            context: 流程上下文

        Returns:
            FileValidationSummary: 验证摘要
        """
        summary = FileValidationSummary(
            session_id=session_id,
            state_name=state.name,
            timestamp=datetime.now(),
        )

        # 获取当前状态的所有上传文件
        all_files = []
        for field_files in context.uploaded_files.values():
            all_files.extend(field_files)

        summary.total_files = len(all_files)

        if not all_files:
            # 检查是否需要文件
            validation_config = self.get_validation_config(state)
            if validation_config and validation_config.required_files:
                required_files = validation_config.required_files
                error = FileValidationError(
                    filename="",
                    error_type=ValidationType.REQUIRED_FILES,
                    error_message="需要上传文件但未找到任何文件",
                    suggestion=f"请上传以下文件：{', '.join([rule.description for rule in required_files])}",
                )
                summary.errors.append(error)
            return summary

        # 获取验证配置
        validation_config = self.get_validation_config(state)
        if not validation_config:
            # 没有验证配置，默认通过
            summary.valid_files = len(all_files)
            summary.is_valid = True
            return summary

        # 执行各种验证
        validation_results = []

        # 1. 验证文件类型和大小
        for file_upload in all_files:
            type_result = await self._validate_file_type(file_upload, validation_config)
            size_result = await self._validate_file_size(file_upload, validation_config)

            validation_results.extend([type_result, size_result])

        # 2. 验证必需文件
        required_result = await self._validate_required_files(
            all_files, validation_config
        )
        validation_results.append(required_result)

        # 3. 验证文件完整性
        for file_upload in all_files:
            integrity_result = await self._validate_file_integrity(file_upload)
            validation_results.append(integrity_result)

        # 4. 多模态验证（如果启用）
        if (
            validation_config.multimodal_config
            and validation_config.multimodal_config.enabled
        ):
            for file_upload in all_files:
                if file_upload.content_type.startswith("image/"):
                    multimodal_result = await self.validate_with_multimodal(
                        file_upload, state, validation_config.multimodal_config
                    )
                    validation_results.append(multimodal_result)

        # 统计结果
        summary.validation_results = validation_results

        # 按文件统计验证结果
        file_validation_status = {}
        for result in validation_results:
            if result.details:
                for filename in result.details:
                    if filename not in file_validation_status:
                        file_validation_status[filename] = []
                    file_validation_status[filename].append(result.status)

        # 计算有效和无效文件数
        valid_files = 0
        invalid_files = 0
        for filename, statuses in file_validation_status.items():
            if ValidationStatus.FAILED in statuses:
                invalid_files += 1
            else:
                valid_files += 1

        summary.valid_files = valid_files
        summary.invalid_files = invalid_files
        summary.is_valid = invalid_files == 0

        # 生成错误列表
        for result in validation_results:
            if result.status == ValidationStatus.FAILED:
                for file_upload in all_files:
                    if result.details and file_upload.filename in result.details:
                        error = FileValidationError(
                            filename=file_upload.filename,
                            error_type=result.validation_type,
                            error_message=result.message,
                            suggestion=result.details.get(file_upload.filename, {}).get(
                                "suggestion"
                            ),
                        )
                        summary.errors.append(error)

        logger.info(
            f"文件验证完成: 会话={session_id}, 状态={state.name}, 有效={summary.valid_files}, 无效={summary.invalid_files}"
        )
        return summary

    def get_validation_config(self, state: StateNode) -> Optional[FileValidationConfig]:
        """从状态节点获取验证配置"""
        if not hasattr(state, "file_validation") or not state.file_validation:
            return None

        # 如果已经是 FileValidationConfig 对象，直接返回
        if isinstance(state.file_validation, FileValidationConfig):
            return state.file_validation

        # 否则处理字典格式的配置
        config_data = state.file_validation
        required_files = []
        for rule_data in config_data.get("required_files", []):
            rule = FileValidationRule(
                name_pattern=rule_data["name_pattern"],
                description=rule_data["description"],
                min_count=rule_data.get("min_count", 1),
                max_count=rule_data.get("max_count"),
            )
            required_files.append(rule)

        naming_convention = None
        if "naming_convention" in config_data:
            naming_convention = NamingConvention(
                pattern=config_data["naming_convention"]["pattern"],
                description=config_data["naming_convention"]["description"],
            )

        # 处理多模态验证配置
        multimodal_config = None
        if "multimodal_config" in config_data:
            multimodal_data = config_data["multimodal_config"]
            multimodal_config = MultimodalValidationConfig(
                enabled=multimodal_data.get("enabled", False),
                confidence_threshold=multimodal_data.get("confidence_threshold", 0.8),
                content_types=multimodal_data.get("content_types", []),
                auto_validate=multimodal_data.get("auto_validate", True),
            )
        elif config_data.get("enable_multimodal", False):
            # 向后兼容旧的 enable_multimodal 配置
            multimodal_config = MultimodalValidationConfig(
                enabled=True,
                confidence_threshold=0.8,
                content_types=[],
                auto_validate=True,
            )

        return FileValidationConfig(
            validation_strategy=config_data.get("validation_strategy", "basic"),
            allowed_types=config_data.get("allowed_types", []),
            max_size_mb=config_data.get("max_size_mb", 10),
            required_files=required_files,
            naming_convention=naming_convention,
            multimodal_config=multimodal_config,
            auto_validate=config_data.get("auto_validate", True),
        )

    async def _validate_file_type(
        self, file_upload: FileUpload, config: FileValidationConfig
    ) -> ValidationResult:
        """验证文件类型"""
        if not config.allowed_types:
            return ValidationResult(
                validation_type=ValidationType.FILE_TYPE,
                status=ValidationStatus.PASSED,
                message="未设置文件类型限制",
            )

        if file_upload.content_type in config.allowed_types:
            return ValidationResult(
                validation_type=ValidationType.FILE_TYPE,
                status=ValidationStatus.PASSED,
                message=f"文件类型 {file_upload.content_type} 符合要求",
            )

        return ValidationResult(
            validation_type=ValidationType.FILE_TYPE,
            status=ValidationStatus.FAILED,
            message=f"文件类型 {file_upload.content_type} 不被允许",
            details={
                file_upload.filename: {
                    "allowed_types": config.allowed_types,
                    "suggestion": f"请上传以下类型的文件：{', '.join(config.allowed_types)}",
                }
            },
        )

    async def _validate_file_size(
        self, file_upload: FileUpload, config: FileValidationConfig
    ) -> ValidationResult:
        """验证文件大小"""
        max_size_bytes = config.max_size_mb * 1024 * 1024

        if file_upload.file_size <= max_size_bytes:
            return ValidationResult(
                validation_type=ValidationType.FILE_SIZE,
                status=ValidationStatus.PASSED,
                message=f"文件大小 {file_upload.file_size} 字节符合要求",
            )

        return ValidationResult(
            validation_type=ValidationType.FILE_SIZE,
            status=ValidationStatus.FAILED,
            message=f"文件大小 {file_upload.file_size} 字节超过限制",
            details={
                file_upload.filename: {
                    "max_size_mb": config.max_size_mb,
                    "actual_size_mb": round(file_upload.file_size / (1024 * 1024), 2),
                    "suggestion": f"文件大小不能超过 {config.max_size_mb} MB",
                }
            },
        )

    async def _validate_file_name(
        self, file_upload: FileUpload, config: FileValidationConfig
    ) -> ValidationResult:
        """验证文件名"""
        if not config.naming_convention:
            return ValidationResult(
                validation_type=ValidationType.FILE_NAME,
                status=ValidationStatus.PASSED,
                message="未设置文件名规范",
            )

        pattern = config.naming_convention.pattern
        # 更智能的模式匹配
        pattern_parts = pattern.split("_")
        filename_parts = (
            file_upload.filename.replace(".jpg", "").replace(".png", "").split("_")
        )

        # 检查是否包含必要的部分
        has_session_id = "{session_id}" in pattern and any(
            part for part in filename_parts if "session" in part.lower()
        )
        has_document_type = "{document_type}" in pattern and len(filename_parts) >= 2
        has_timestamp = "{timestamp}" in pattern and any(
            part for part in filename_parts if part.isdigit() and len(part) >= 8
        )

        # 如果匹配了关键元素，认为符合规范
        if (
            (has_session_id or "{session_id}" not in pattern)
            and (has_document_type or "{document_type}" not in pattern)
            and (has_timestamp or "{timestamp}" not in pattern)
        ):
            return ValidationResult(
                validation_type=ValidationType.FILE_NAME,
                status=ValidationStatus.PASSED,
                message=f"文件名 {file_upload.filename} 符合命名规范",
            )

        return ValidationResult(
            validation_type=ValidationType.FILE_NAME,
            status=ValidationStatus.FAILED,
            message=f"文件名 {file_upload.filename} 不符合命名规范",
            details={
                file_upload.filename: {
                    "expected_pattern": pattern,
                    "suggestion": f"文件名应遵循以下模式：{pattern}",
                }
            },
        )

    async def _validate_required_files(
        self, files: List[FileUpload], config: FileValidationConfig
    ) -> ValidationResult:
        """验证必需文件"""
        if not config.required_files:
            return ValidationResult(
                validation_type=ValidationType.REQUIRED_FILES,
                status=ValidationStatus.PASSED,
                message="无必需文件要求",
            )

        missing_files = []
        for rule in config.required_files:
            matching_files = []
            for f in files:
                try:
                    if re.search(rule.name_pattern, f.filename, re.IGNORECASE):
                        matching_files.append(f)
                except re.error as e:
                    logger.warning(
                        f"正则表达式匹配失败: {rule.name_pattern}, 错误: {e}"
                    )
                    # 如果正则表达式无效，尝试简单的字符串包含匹配
                    if (
                        rule.name_pattern.lower().replace("*", "").replace(".", "")
                        in f.filename.lower()
                    ):
                        matching_files.append(f)

            if len(matching_files) < rule.min_count:
                missing_files.append(
                    f"{rule.description} (至少需要 {rule.min_count} 个, 当前 {len(matching_files)} 个)"
                )
            elif rule.max_count and len(matching_files) > rule.max_count:
                missing_files.append(
                    f"{rule.description} (最多允许 {rule.max_count} 个, 当前 {len(matching_files)} 个)"
                )

        if missing_files:
            return ValidationResult(
                validation_type=ValidationType.REQUIRED_FILES,
                status=ValidationStatus.FAILED,
                message=f"缺少必需文件: {', '.join(missing_files)}",
                details={
                    "missing_requirements": missing_files,
                    "suggestion": "请上传所有必需的文件",
                },
            )

        return ValidationResult(
            validation_type=ValidationType.REQUIRED_FILES,
            status=ValidationStatus.PASSED,
            message="所有必需文件已上传",
        )

    async def _validate_file_integrity(
        self, file_upload: FileUpload
    ) -> ValidationResult:
        """验证文件完整性"""
        try:
            file_path = Path(file_upload.file_path)
            if not file_path.exists():
                return ValidationResult(
                    validation_type=ValidationType.FILE_INTEGRITY,
                    status=ValidationStatus.FAILED,
                    message=f"文件 {file_upload.filename} 不存在",
                    details={
                        file_upload.filename: {
                            "suggestion": "请重新上传文件",
                        }
                    },
                )

            # 检查文件是否可读
            async with aiofiles.open(file_path, "rb") as f:
                await f.read(1024)  # 尝试读取前1KB

            return ValidationResult(
                validation_type=ValidationType.FILE_INTEGRITY,
                status=ValidationStatus.PASSED,
                message=f"文件 {file_upload.filename} 完整性检查通过",
            )

        except Exception as e:
            return ValidationResult(
                validation_type=ValidationType.FILE_INTEGRITY,
                status=ValidationStatus.FAILED,
                message=f"文件 {file_upload.filename} 完整性检查失败: {str(e)}",
                details={
                    file_upload.filename: {
                        "error": str(e),
                        "suggestion": "请重新上传文件",
                    }
                },
            )

    async def validate_with_multimodal(
        self,
        file_upload: FileUpload,
        state: StateNode,
        multimodal_config: MultimodalValidationConfig,
    ) -> ValidationResult:
        """使用多模态模型验证文件内容"""
        try:
            # 这里应该调用多模态模型API
            # 目前返回模拟结果
            result = MultimodalValidationResult(
                filename=file_upload.filename,
                content_type=file_upload.content_type,
                confidence_score=0.85,
                content_description="检测到身份证照片",
                detected_objects=["身份证", "人像"],
                extracted_text="姓名：张三\n身份证号：123456789",
                is_valid_content=True,
                validation_message="文件内容验证通过",
            )

            # 检查置信度是否满足阈值要求
            if result.confidence_score < multimodal_config.confidence_threshold:
                return ValidationResult(
                    validation_type=ValidationType.MULTIMODAL,
                    status=ValidationStatus.FAILED,
                    message=f"多模态验证失败: 置信度 {result.confidence_score} 低于阈值 {multimodal_config.confidence_threshold}",
                    details={
                        file_upload.filename: {
                            "confidence_score": result.confidence_score,
                            "threshold": multimodal_config.confidence_threshold,
                            "suggestion": "请上传更清晰的文件照片",
                        }
                    },
                )

            # 检查内容类型是否符合要求
            if multimodal_config.content_types:
                content_match = False
                for expected_type in multimodal_config.content_types:
                    if expected_type.lower() in result.content_description.lower():
                        content_match = True
                        break

                if not content_match:
                    return ValidationResult(
                        validation_type=ValidationType.MULTIMODAL,
                        status=ValidationStatus.FAILED,
                        message=f"多模态验证失败: 内容类型不匹配，期望 {multimodal_config.content_types}",
                        details={
                            file_upload.filename: {
                                "expected_types": multimodal_config.content_types,
                                "detected_content": result.content_description,
                                "suggestion": f"请上传包含以下内容的文件：{', '.join(multimodal_config.content_types)}",
                            }
                        },
                    )

            if result.is_valid_content:
                return ValidationResult(
                    validation_type=ValidationType.MULTIMODAL,
                    status=ValidationStatus.PASSED,
                    message=f"多模态验证通过: {result.validation_message}",
                    details={
                        file_upload.filename: {
                            "confidence_score": result.confidence_score,
                            "content_description": result.content_description,
                        }
                    },
                )
            else:
                return ValidationResult(
                    validation_type=ValidationType.MULTIMODAL,
                    status=ValidationStatus.FAILED,
                    message=f"多模态验证失败: {result.validation_message}",
                    details={
                        file_upload.filename: {
                            "confidence_score": result.confidence_score,
                            "suggestion": "请上传清晰的文件照片",
                        }
                    },
                )

        except Exception as e:
            logger.error(f"多模态验证失败: {e}")
            # 当多模态验证跳过时，使用LLM进行内容验证
            try:
                llm_prompt = f"""
                请分析以下文件内容，判断它是否包含以下内容类型: {", ".join(multimodal_config.content_types)}
                
                文件名: {file_upload.filename}
                文件类型: {file_upload.content_type}
                
                请以JSON格式返回验证结果:
                {{
                    "is_valid_content": true/false,
                    "confidence_score": 0.0-1.0,
                    "content_description": "内容描述",
                    "suggestion": "建议"
                }}
                """

                llm_response = await default_ai_engine.get_chat_response(llm_prompt)

                # 解析LLM响应（简化处理）
                if "身份证" in llm_response or "identity_card" in llm_response.lower():
                    return ValidationResult(
                        validation_type=ValidationType.MULTIMODAL,
                        status=ValidationStatus.PASSED,
                        message=f"LLM验证通过: 检测到期望的内容类型",
                        details={
                            file_upload.filename: {
                                "confidence_score": 0.9,
                                "content_description": "检测到身份证相关内容",
                            }
                        },
                    )
                else:
                    return ValidationResult(
                        validation_type=ValidationType.MULTIMODAL,
                        status=ValidationStatus.FAILED,
                        message=f"LLM验证失败: 未检测到期望的内容类型",
                        details={
                            file_upload.filename: {
                                "confidence_score": 0.3,
                                "suggestion": f"请上传包含以下内容的文件：{', '.join(multimodal_config.content_types)}",
                            }
                        },
                    )

            except Exception as llm_e:
                logger.error(f"LLM验证也失败: {llm_e}")
                return ValidationResult(
                    validation_type=ValidationType.MULTIMODAL,
                    status=ValidationStatus.SKIPPED,
                    message=f"多模态验证和LLM验证均跳过: {str(e)}",
                )

    async def suggest_file_rename(
        self, file_upload: FileUpload, session_id: str, document_type: str
    ) -> str:
        """建议文件重命名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        extension = Path(file_upload.filename).suffix
        suggested_name = f"{session_id}_{document_type}_{timestamp}{extension}"
        return suggested_name

    async def get_file_validation_suggestions(
        self, validation_summary: FileValidationSummary
    ) -> List[str]:
        """获取文件验证建议"""
        suggestions = []

        if not validation_summary.is_valid:
            for error in validation_summary.errors:
                if error.suggestion:
                    suggestions.append(f"文件 {error.filename}: {error.suggestion}")
                else:
                    suggestions.append(f"文件 {error.filename}: {error.error_message}")

        return suggestions


# 创建默认的文件验证器实例
default_file_validator = FileValidator()
