"""
意图识别模块 - 识别用户输入的意图并控制流程流转
"""

from loguru import logger

from api.schemas import IntentType
from models.context import FlowContext
from models.state import StateNode
from utils.ai_engine import default_ai_engine


async def recognize_intent(
    text: str, context: FlowContext, current_state: StateNode
) -> IntentType:
    """
    对输入文本进行意图分类
    Args:
        text: 输入文本
        context: 会话上下文
        current_state: 当前状态
    Returns:
        IntentType: 识别出的意图类型
    """
    try:
        # 使用大语言模型进行意图识别
        intent_type = await default_ai_engine.get_intent(text, context, current_state)
        logger.info(f"LLM意图识别结果: {text} -> {intent_type.value}")
        return intent_type
    except Exception as e:
        # 如果LLM调用失败，可以根据需要决定是否回退或直接失败
        logger.error(f"LLM意图识别失败: {e}，返回 UNKNOWN")
        return IntentType.UNKNOWN
