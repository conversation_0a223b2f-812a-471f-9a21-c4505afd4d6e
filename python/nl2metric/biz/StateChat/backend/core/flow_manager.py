"""
流程配置管理器
"""

from typing import Dict, List

from core.exceptions import FlowConfigError
from models.state import FlowConfig
from utils.config_loader import ConfigLoader
from utils.app_config import settings


class FlowManager:
    """
    管理所有可用的流程配置
    """

    def __init__(self):
        """
        初始化FlowManager，加载所有流程配置
        """
        self._flows: Dict[str, FlowConfig] = self._load_flows()
        if not self._flows:
            raise FlowConfigError(
                "未能加载任何流程配置，请检查 'config/flow_config' 目录。"
            )

        default_flow_name = settings.app.default_flow_name
        if default_flow_name not in self._flows:
            # 如果指定的默认流程不存在，则使用第一个加载的流程作为默认
            self.default_flow_name: str = next(iter(self._flows.keys()))
        else:
            self.default_flow_name: str = default_flow_name

    def _load_flows(self) -> Dict[str, FlowConfig]:
        """
        从配置目录加载所有流程配置
        """
        configs = ConfigLoader.load_all_flow_configs()
        return {config.flow_name: config for config in configs}

    def get_flow_by_name(self, name: str) -> FlowConfig:
        """
        按名称获取流程配置
        Args:
            name: 流程名称
        Returns:
            FlowConfig: 对应的流程配置对象
        Raises:
            FlowConfigError: 如果找不到指定的流程
        """
        flow = self._flows.get(name)
        if not flow:
            raise FlowConfigError(f"未找到名为 '{name}' 的流程。")
        return flow

    def get_default_flow(self) -> FlowConfig:
        """
        获取默认的流程配置
        """
        return self.get_flow_by_name(self.default_flow_name)

    def list_flows(self) -> List[FlowConfig]:
        """
        列出所有可用的流程配置
        """
        return list(self._flows.values())


# 创建一个单例的 flow_manager 实例，在应用启动时加载所有配置
flow_manager = FlowManager()
