"""
基于LangGraph的状态机实现
"""

import asyncio
import json
import re
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, TypedDict, Union

import yaml
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from loguru import logger
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from api.schemas import ProcessInputResponse
from models.context import FlowContext
from models.session import UserSession, SessionStatus
from models.state import FlowConfig, StateNode, UserInput, InputType, StateTransition
from storage.session_store import SessionStore, ContextStore
from utils.ai_engine import default_ai_engine
from .exceptions import StateNotFoundError, SessionNotFoundError, FlowConfigError
from .intent import IntentType


class LangGraphChatState(TypedDict):
    """LangGraph聊天状态"""
    session_id: str
    current_state: str
    messages: List[Union[HumanMessage, AIMessage, SystemMessage]]
    user_data: Dict[str, Any]
    collected_files: List[Dict[str, Any]]
    flow_config: Dict[str, Any]
    is_complete: bool
    user_input: Optional[str]
    next_state: Optional[str]
    error_message: Optional[str]


class LangGraphStateNode:
    """LangGraph状态节点处理类"""

    def __init__(self, node_config: StateNode, ai_engine=None):
        self.config = node_config
        self.name = node_config.name
        self.prompt = node_config.prompt
        self.input_type = node_config.input_type
        self.ai_engine = ai_engine or default_ai_engine

    async def process_input(self, user_input: str, state: LangGraphChatState) -> Dict[str, Any]:
        """处理用户输入"""
        try:
            if self.input_type == InputType.CHOICE:
                return await self._process_choice_input(user_input, state)
            elif self.input_type == InputType.TEXT:
                return await self._process_text_input(user_input, state)
            elif self.input_type == InputType.FILE:
                return await self._process_file_input(user_input, state)
            elif self.input_type == InputType.AUTO:
                return await self._process_auto_input(user_input, state)
            else:
                return {"next_state": None, "error": f"不支持的输入类型: {self.input_type}"}
        except Exception as e:
            logger.error(f"处理输入时发生错误: {e}")
            return {"next_state": None, "error": f"处理输入时发生错误: {str(e)}"}

    async def _process_choice_input(self, user_input: str, state: LangGraphChatState) -> Dict[str, Any]:
        """处理选择输入"""
        options = self.config.options or []
        
        # 使用AI引擎进行选择匹配
        if self.config.llm_prompt_hint:
            try:
                choice = await self.ai_engine.get_llm_choice(
                    prompt_hint=self.config.llm_prompt_hint,
                    options=options,
                    user_input=user_input
                )
            except Exception as e:
                logger.error(f"LLM选择处理失败: {e}")
                choice = None
        else:
            choice = None

        # 如果LLM没有返回有效选择，尝试直接匹配
        if not choice:
            for option in options:
                if option.lower() in user_input.lower() or user_input.lower() in option.lower():
                    choice = option
                    break

        if choice and isinstance(self.config.next, dict) and choice in self.config.next:
            return {"next_state": self.config.next[choice]}
        
        return {"next_state": None, "error": f"请选择有效选项: {', '.join(options)}"}

    async def _process_text_input(self, user_input: str, state: LangGraphChatState) -> Dict[str, Any]:
        """处理文本输入"""
        result = {"next_state": self.config.next if isinstance(self.config.next, str) else None}

        # 如果需要提取字段
        if self.config.requires:
            try:
                extracted_data = await self.ai_engine.extract_data(
                    text=user_input,
                    required_fields=self.config.requires,
                    llm_prompt_hint=self.config.llm_prompt_hint or ""
                )
                
                result["extracted_data"] = extracted_data
                
                # 检查是否所有必需字段都已提取
                missing_fields = extracted_data.get("missing_fields", [])
                if missing_fields:
                    followup_message = extracted_data.get("followup_message", "")
                    if not followup_message:
                        followup_message = f"请提供以下信息: {', '.join(missing_fields)}"
                    result["error"] = followup_message
                    result["next_state"] = None  # 保持当前状态
                    
            except Exception as e:
                logger.error(f"数据提取失败: {e}")
                result["error"] = "数据提取失败，请重新输入"
                result["next_state"] = None

        return result

    async def _process_file_input(self, user_input: str, state: LangGraphChatState) -> Dict[str, Any]:
        """处理文件输入"""
        # 文件输入通常通过其他方式处理，这里主要处理文本确认
        return {"next_state": self.config.next if isinstance(self.config.next, str) else None}

    async def _process_auto_input(self, user_input: str, state: LangGraphChatState) -> Dict[str, Any]:
        """处理自动转移状态"""
        # 自动状态直接转移到下一个状态
        return {"next_state": self.config.next if isinstance(self.config.next, str) else None}


class LangGraphStateMachine:
    """基于LangGraph的状态机"""

    def __init__(self, flow_config: FlowConfig, db_session: AsyncSession):
        self.flow_config = flow_config
        self.db_session = db_session
        self.session_store = SessionStore(db_session)
        self.context_store = ContextStore(db_session)
        
        # 创建状态节点
        self.nodes = {
            name: LangGraphStateNode(node_config)
            for name, node_config in flow_config.states.items()
        }
        
        # 构建LangGraph
        self.graph = self._build_graph()
        
        logger.info(f"LangGraph状态机初始化完成: {flow_config.flow_name} v{flow_config.version}")

    def _build_graph(self) -> StateGraph:
        """构建LangGraph状态图"""
        workflow = StateGraph(LangGraphChatState)

        # 添加所有节点
        for node_name in self.flow_config.states.keys():
            workflow.add_node(node_name, self._create_node_handler(node_name))

        # 设置入口点
        workflow.set_entry_point(self.flow_config.initial_state)

        # 添加边
        for node_name, node_config in self.flow_config.states.items():
            next_config = node_config.next

            if next_config is None:
                # 终止状态
                workflow.add_edge(node_name, END)
            elif isinstance(next_config, str):
                # 单一 next 状态
                workflow.add_edge(node_name, next_config)
            elif isinstance(next_config, dict):
                # 条件转移
                # 创建边映射，包括所有可能的目标状态
                edge_mapping = {}
                for choice, target_state in next_config.items():
                    edge_mapping[choice] = target_state or END

                # 添加所有目标状态作为可能的边
                for target_state in next_config.values():
                    if target_state and target_state not in edge_mapping:
                        edge_mapping[target_state] = target_state

                workflow.add_conditional_edges(
                    node_name,
                    self._create_router(node_name, next_config),
                    edge_mapping,
                )

        return workflow.compile()

    def _create_node_handler(self, node_name: str):
        """创建节点处理器"""
        
        async def handler(state: LangGraphChatState) -> LangGraphChatState:
            node = self.nodes[node_name]
            
            # 更新当前状态
            state["current_state"] = node_name
            
            # 添加系统提示消息
            system_msg = SystemMessage(content=node.prompt)
            state["messages"].append(system_msg)
            
            # 如果是自动状态，直接处理
            if node.input_type == InputType.AUTO:
                result = await node.process_input("", state)
                if result.get("next_state"):
                    state["next_state"] = result["next_state"]
                    
            return state
            
        return handler

    def _create_router(self, node_name: str, next_config: Dict[str, str]):
        """创建路由器"""
        
        async def router(state: LangGraphChatState) -> str:
            # 如果有错误消息，保持当前状态
            if state.get("error_message"):
                return node_name
                
            # 如果已经指定了下一个状态，使用它
            if state.get("next_state"):
                next_state = state["next_state"]
                state["next_state"] = None  # 清除
                return next_state
                
            # 获取用户输入
            user_input = state.get("user_input", "")
            if not user_input:
                # 如果没有用户输入，返回默认状态
                return list(next_config.values())[0] if next_config else END
                
            node = self.nodes[node_name]
            
            # 处理输入并确定下一个状态
            result = await node.process_input(user_input, state)
            
            if "error" in result:
                # 如果有错误，保持在当前状态
                state["error_message"] = result["error"]
                return node_name
                
            if result.get("next_state"):
                # 保存提取的数据
                if "extracted_data" in result:
                    extracted = result["extracted_data"]
                    # 移除元数据字段
                    for key in ["missing_fields", "followup_message"]:
                        extracted.pop(key, None)
                    state["user_data"].update(extracted)
                    
                return result["next_state"]
                
            # 默认返回第一个选项
            return list(next_config.values())[0] if next_config else END
            
        return router

    async def create_session(self, user_id: str, flow_name: str = None) -> UserSession:
        """创建新会话"""
        session_id = str(uuid.uuid4())
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            flow_name=flow_name or self.flow_config.flow_name,
            current_state=self.flow_config.initial_state,
            status=SessionStatus.ACTIVE,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        await self.session_store.create_session(session)
        
        # 创建初始上下文
        context = FlowContext(
            session_id=session_id,
            flow_name=flow_name or self.flow_config.flow_name,
            collected_data={},
            file_records=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        await self.context_store.create_context(context)
        
        logger.info(f"创建新会话: {session_id}, 用户: {user_id}, 初始状态: {self.flow_config.initial_state}")
        return session

    async def get_session(self, session_id: str) -> UserSession:
        """获取会话"""
        session = await self.session_store.get_session(session_id)
        if not session:
            raise SessionNotFoundError(session_id)
        return session

    async def get_context(self, session_id: str) -> FlowContext:
        """获取上下文"""
        context = await self.context_store.get_context(session_id)
        if not context:
            raise SessionNotFoundError(f"Context not found for session: {session_id}")
        return context

    async def process_user_input(self, session_id: str, user_input: UserInput) -> ProcessInputResponse:
        """处理用户输入"""
        try:
            # 获取会话和上下文
            session = await self.get_session(session_id)
            context = await self.get_context(session_id)

            # 构建LangGraph状态
            # 合并所有用户数据
            user_data = {}
            user_data.update(context.business_info or {})
            user_data.update(context.personal_info or {})
            user_data.update(context.license_info or {})

            # 转换表单数据
            for field_name, form_data in (context.form_data or {}).items():
                user_data[field_name] = form_data.field_value

            state: LangGraphChatState = {
                "session_id": session_id,
                "current_state": session.current_state,
                "messages": [],
                "user_data": user_data,
                "collected_files": [],  # TODO: 转换上传文件格式
                "flow_config": self.flow_config.model_dump(),
                "is_complete": False,
                "user_input": user_input.value,
                "next_state": None,
                "error_message": None
            }

            # 添加用户消息
            if user_input.value:
                state["messages"].append(HumanMessage(content=user_input.value))

            # 运行LangGraph
            result_state = await self.graph.ainvoke(state)

            # 更新会话状态
            new_state = result_state.get("current_state", session.current_state)
            session.current_state = new_state
            session.updated_at = datetime.now()
            await self.session_store.update_session(session)

            # 更新上下文
            updated_user_data = result_state.get("user_data", {})
            # 将用户数据分类存储到不同字段
            # 这里可以根据具体业务逻辑来分类，暂时存储到 business_info
            if updated_user_data:
                context.business_info.update(updated_user_data)
            context.updated_at = datetime.now()
            await self.context_store.update_context(context)

            # 记录状态转移
            if new_state != session.current_state:
                transition = StateTransition(
                    from_state=session.current_state,
                    to_state=new_state,
                    user_input=user_input.model_dump()
                )
                await self.session_store.add_transition(session_id, transition)

            # 获取新状态信息
            current_state_node = self.flow_config.states.get(new_state)
            if not current_state_node:
                raise StateNotFoundError(new_state)

            # 检查是否完成
            is_complete = new_state in self.flow_config.final_states
            if is_complete:
                session.status = SessionStatus.COMPLETED
                await self.session_store.update_session(session)

            # 计算进度
            progress = self._calculate_progress(new_state)

            return ProcessInputResponse(
                success=True,
                completed=is_complete,
                processed_state=self.flow_config.states[session.current_state],
                current_state=current_state_node,
                next_states=self._get_next_states(current_state_node),
                progress=progress,
                error_message=result_state.get("error_message")
            )

        except Exception as e:
            logger.error(f"处理用户输入失败: {e}")
            raise

    def _get_next_states(self, current_state: StateNode) -> List[StateNode]:
        """获取可能的下一个状态"""
        next_states = []
        if isinstance(current_state.next, str):
            next_state = self.flow_config.states.get(current_state.next)
            if next_state:
                next_states.append(next_state)
        elif isinstance(current_state.next, dict):
            for target_state_name in current_state.next.values():
                if target_state_name:
                    target_state = self.flow_config.states.get(target_state_name)
                    if target_state:
                        next_states.append(target_state)
        return next_states

    def _calculate_progress(self, current_state: str) -> float:
        """计算流程进度"""
        try:
            # 简单的进度计算：当前状态在所有状态中的位置
            all_states = list(self.flow_config.states.keys())
            if current_state in all_states:
                return (all_states.index(current_state) + 1) / len(all_states) * 100
            return 0.0
        except Exception:
            return 0.0

    async def get_current_state(self, session_id: str) -> StateNode:
        """获取当前状态节点"""
        session = await self.get_session(session_id)
        current_state_name = session.current_state
        if current_state_name not in self.flow_config.states:
            raise StateNotFoundError(current_state_name)
        return self.flow_config.states[current_state_name]
