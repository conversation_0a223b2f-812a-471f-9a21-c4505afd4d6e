"""
演示如何使用提示模板进行LLM处理
"""

import asyncio
import json
import yaml
from typing import Dict, List, Any, Optional
from datetime import datetime
from jinja2 import Template


class PromptTemplateDemo:
    """演示提示模板的使用"""

    def __init__(self):
        self.prompts_dir = "prompts"
        self._load_prompt_templates()

    def _load_prompt_templates(self):
        """加载提示模板"""
        self.choice_template = self._load_template("llm_choice_template.jinja2")
        self.param_template = self._load_template(
            "parameter_extraction_template.jinja2"
        )

    def _load_template(self, filename: str) -> Template:
        """加载Jinja2模板"""
        path = os.path.join(self.prompts_dir, filename)
        with open(path, "r", encoding="utf-8") as f:
            return Template(f.read())

    def demo_choice_processing(self):
        """演示选择处理的提示模板"""
        print("=== 分支选择提示模板演示 ===\n")

        # 示例1：注册类型选择
        prompt_hint = "从用户输入中判断注册类型是'个人'还是'企业'。如果用户表达了个人身份，选择'个人'；如果表达了公司或组织身份，选择'企业'。"
        user_input = "我想注册个人账号"
        options = ["个人", "企业"]

        prompt = self.choice_template.render(
            prompt_hint=prompt_hint, user_input=user_input, options=options
        )

        print("【提示模板生成的提示】")
        print(prompt)
        print("\n" + "=" * 60 + "\n")

        # 示例2：服务选择
        prompt_hint = ""
        user_input = "我想办理服务A"
        options = ["服务A", "服务B", "服务C"]

        prompt = self.choice_template.render(
            prompt_hint=prompt_hint, user_input=user_input, options=options
        )

        print("【服务选择提示】")
        print(prompt)
        print("\n" + "=" * 60 + "\n")

    def demo_parameter_extraction(self):
        """演示参数提取的提示模板"""
        print("=== 参数提取提示模板演示 ===\n")

        # 示例1：个人信息提取
        prompt_hint = """从文本提取结构化信息：
1. 姓名（中文或英文全名）
2. 手机号（11位数字）
3. 邮箱（符合标准邮箱格式）
缺失时追问具体字段"""
        required_fields = ["name", "email", "phone"]
        user_input = "我叫王小明，邮箱是************************"
        context_info = {}

        format_example = {
            "name": "张三",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "missing_fields": ["phone"],
            "follow_up_message": "请补充手机号",
        }

        prompt = self.param_template.render(
            context_info=json.dumps(context_info, ensure_ascii=False),
            prompt_hint=prompt_hint,
            required_fields=required_fields,
            user_input=user_input,
            format_instructions=json.dumps(format_example, ensure_ascii=False),
        )

        print("【个人信息提取提示】")
        print(prompt)
        print("\n" + "=" * 60 + "\n")

        # 示例2：企业信息提取
        prompt_hint = """提取企业信息字段：
1. 公司名称（含'公司'或'有限'等关键字）
2. 业务类型（描述性短语）
3. 联系人邮箱"""
        required_fields = ["company_name", "business_type", "contact_email"]
        user_input = (
            "北京科技有限公司，主要从事软件开发业务，联系邮箱是******************"
        )
        context_info = {}

        prompt = self.param_template.render(
            context_info=json.dumps(context_info, ensure_ascii=False),
            prompt_hint=prompt_hint,
            required_fields=required_fields,
            user_input=user_input,
            format_instructions=json.dumps(format_example, ensure_ascii=False),
        )

        print("【企业信息提取提示】")
        print(prompt)
        print("\n" + "=" * 60 + "\n")

    def demo_flow_integration(self):
        """演示与流程配置的集成"""
        print("=== 流程配置集成演示 ===\n")

        # 加载流程配置
        with open(
            "config/flow_config/simple_test_flow.yaml", "r", encoding="utf-8"
        ) as f:
            config = yaml.safe_load(f)

        # 显示各状态的提示信息
        for state_name, state_config in config["states"].items():
            if "llm_prompt_hint" in state_config:
                print(f"【状态：{state_name}】")
                print(f"输入类型：{state_config['input_type']}")
                print(f"LLM提示：{state_config['llm_prompt_hint']}")

                if state_config["input_type"] == "choice":
                    print(f"选项：{state_config['options']}")
                elif "requires" in state_config:
                    print(f"需提取字段：{state_config['requires']}")

                print("\n" + "-" * 40 + "\n")


def demo_template_usage():
    """演示模板使用"""
    demo = PromptTemplateDemo()

    print("提示模板集成演示\n")
    print("=" * 60)

    # 演示分支选择
    demo.demo_choice_processing()

    # 演示参数提取
    demo.demo_parameter_extraction()

    # 演示流程集成
    demo.demo_flow_integration()

    print("\n=== 总结 ===")
    print("1. 分支选择通过 llm_choice_template.jinja2 实现")
    print("2. 参数提取通过 parameter_extraction_template.jinja2 实现")
    print("3. 意图识别通过 intent_template.jinja2 实现")
    print("4. 流程配置中的 llm_prompt_hint 字段提供LLM处理的上下文")
    print("5. requires 字段指定需要从用户输入中提取的结构化信息")
    print("\n实际使用时，将这些提示发送给LLM，即可实现智能的分支选择和参数提取。")


if __name__ == "__main__":
    import os

    demo_template_usage()
