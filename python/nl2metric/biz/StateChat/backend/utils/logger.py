"""
日志工具配置
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from loguru import logger


def setup_logger(log_level: str = "INFO", log_dir: str = "logs"):
    """
    配置日志系统

    Args:
        log_level: 日志级别
        log_dir: 日志目录路径，可选
    """
    # 移除默认的日志处理器
    logger.remove()

    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True,
    )

    # 添加文件输出（如果指定了日志目录路径）
    if log_dir:
        log_file = Path(log_dir) / "state_chat.log"
        Path(log_dir).mkdir(parents=True, exist_ok=True)

        logger.add(
            log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",  # 日志文件大小达到10MB时轮转
            retention="30 days",  # 保留30天的日志
            compression="zip",  # 压缩旧日志文件
        )

    # 设置 "sqlalchemy.engine" 的日志级别为 WARNING，以减少不必要的日志输出
    sqlalchemy_logger = logging.getLogger("sqlalchemy.engine")
    sqlalchemy_logger.setLevel(logging.WARNING)
    
    # 为 sqlalchemy_logger 添加处理器，使其日志能够输出到控制台和文件
    if not sqlalchemy_logger.handlers:
        # 控制台输出
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter(
            "{asctime} | {levelname: <8} | {name}:{filename}:{lineno} - {message}",
            style="{"
        ))
        sqlalchemy_logger.addHandler(handler)
        
        # 文件输出（如果指定了日志目录路径）
        if log_dir:
            # 文件输出，添加轮转、保留功能
            sqlalchemy_log_file = Path(log_dir) / "sqlalchemy.log"
            sqlalchemy_file_handler = logging.handlers.RotatingFileHandler(
                sqlalchemy_log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=10  # 保留10个备份文件
            )
            sqlalchemy_file_handler.setFormatter(logging.Formatter(
                "{asctime} | {levelname: <8} | {name}:{filename}:{lineno} - {message}",
                style="{"
            ))
            sqlalchemy_logger.addHandler(sqlalchemy_file_handler)
        
        sqlalchemy_logger.propagate = False  # 防止重复记录

    # 设置特定库的日志级别并添加处理器
    openai_logger = logging.getLogger("openai")
    openai_logger.setLevel(log_level)
    
    # 为 openai_logger 添加处理器，使其日志能够输出到控制台和文件
    if not openai_logger.handlers:
        # 控制台输出
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter(
            "{asctime} | {levelname: <8} | {name}:{filename}:{lineno} - {message}",
            style="{"
        ))
        openai_logger.addHandler(handler)
        if log_dir:
            # 文件输出，添加轮转、保留和压缩功能
            openai_log_file = Path(log_dir) / "openai.log"
            openai_file_handler = logging.handlers.RotatingFileHandler(
                openai_log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=30  # 保留30个备份文件
            )
            openai_file_handler.setFormatter(logging.Formatter(
                "{asctime} | {levelname: <8} | {name}:{filename}:{lineno} - {message}",
                style="{"
            ))
            openai_logger.addHandler(openai_file_handler)
        
        openai_logger.propagate = False  # 防止重复记录

    logging.getLogger("httpcore").setLevel(logging.WARNING)

    logger.info(f"日志系统初始化完成，级别: {log_level}")
    logger.info(f"已启用OpenAI日志记录，级别: {log_level}")
    logger.info(f"日志系统初始化完成，级别: {log_level}")


def get_logger(name: str = None):
    """
    获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        logger: 日志记录器实例
    """
    if name:
        return logger.bind(name=name)
    return logger