"""
AI Engine: 封装与大语言模型交互的核心逻辑
"""

import os
from pathlib import Path
from typing import Dict, Any, AsyncGenerator, List, Optional

import yaml
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from langchain_openai import ChatOpenAI
from loguru import logger
from pydantic import create_model, Field

from api.schemas import IntentType
from core.flow_manager import flow_manager
from models.context import ChatMessage, FlowContext, MessageRole
from models.state import StateNode
from utils import flatten_str


class AIEngine:
    """封装与大语言模型交互的核心逻辑"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化AI引擎。

        Args:
            config: OpenAI的配置字典。如果未提供，将从默认路径加载。
        """
        self.project_root = Path(__file__).parent.parent
        if config is None:
            config_path = self.project_root / "config" / "config.yaml"
            self.config = self._load_openai_config(str(config_path))
        else:
            self.config = config

        self._setup_environment()
        self._initialize_models()
        self._initialize_chains()

    def _load_openai_config(self, config_path: str) -> Dict[str, Any]:
        """从主配置文件加载OpenAI相关的配置"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                full_config = yaml.safe_load(f)
            return full_config.get("openai", {})
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            raise
        except Exception as e:
            logger.error(f"加载OpenAI配置失败: {e}")
            raise

    def _setup_environment(self):
        """设置OpenAI相关的环境变量"""
        os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY") or self.config.get(
            "api", {}
        ).get("api_key")
        if "base_url" in self.config.get("api", {}):
            os.environ["OPENAI_API_BASE"] = self.config["api"]["base_url"]
        if "organization" in self.config.get("api", {}):
            os.environ["OPENAI_ORGANIZATION"] = self.config["api"]["organization"]

    def _initialize_models(self):
        """根据配置初始化LLM模型"""
        self.chat_model = self._create_chat_model("chat")
        self.intent_model = self._create_chat_model("intent")

    def _create_chat_model(self, model_key: str) -> ChatOpenAI:
        """创建ChatOpenAI实例的辅助函数"""
        model_config = self.config["models"][model_key]
        return ChatOpenAI(
            model=model_config["name"],
            temperature=model_config["temperature"],
            max_tokens=model_config["max_tokens"],
            streaming=True,  # 默认启用流式
            extra_body=model_config.get("extra_body", {}),
            max_retries=model_config.get("max_retries", 3),
            timeout=model_config.get("request_timeout", 30),
        )

    def _initialize_chains(self):
        """初始化LangChain链"""
        # 意图识别Chain
        intent_template = self.load_prompt_template("intent_template")
        self.intent_prompt = ChatPromptTemplate.from_template(intent_template)
        self.intent_chain = self.intent_prompt | self.intent_model | JsonOutputParser()

        # 聊天Chain
        self.chat_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", "{system_prompt}"),
                MessagesPlaceholder(variable_name="chat_history"),
                ("user", "{text}"),
            ]
        )
        self.chat_chain = self.chat_prompt | self.chat_model | StrOutputParser()

    def load_prompt_template(self, template_name: str) -> str:
        """从文件加载指定的提示模板"""
        # 确保在配置文件中查找新的模板key

        template_path_str = self.config["prompts"][template_name]

        template_path = self.project_root / template_path_str
        try:
            return template_path.read_text(encoding="utf-8")
        except FileNotFoundError:
            logger.error(f"提示模板文件未找到: {template_path}")
            raise
        except Exception as e:
            logger.error(f"加载提示模板失败: {e}")
            raise

    async def get_intent(
        self, text: str, context: FlowContext, current_state: StateNode
    ) -> IntentType:
        """获取意图分类"""
        chat_history = context.get_chat_history()
        history_dialog = [msg.model_dump_json() for msg in chat_history[-3:]]

        try:
            result = await self.intent_chain.ainvoke(
                {
                    "user_input": text,
                    "current_stage": flatten_str(current_state),
                    "history_dialog": history_dialog,
                    "flow_description": flow_manager.get_flow_by_name(
                        context.flow_name
                    ).description,
                }
            )
            logger.info(f"意图识别完成: {text} -> {result}")

            intent_type_str = result.get("意图类型")
            condensed_question = result.get("condensed question")

            if condensed_question:
                context.condensed_question = condensed_question

            try:
                return IntentType(intent_type_str)
            except ValueError:
                logger.exception(
                    f"LLM返回的意图类型无效: {intent_type_str}，返回 UNKNOWN"
                )
                return IntentType.UNKNOWN

        except Exception as e:
            logger.exception(f"意图识别调用失败: {e}")
            raise

    async def get_chat_response_stream(
        self, text: str, chat_history: Optional[List[ChatMessage]] = None
    ) -> AsyncGenerator[str, None]:
        """获取聊天回复 (流式)"""
        langchain_chat_history = self._format_chat_history(chat_history)
        try:
            async for chunk in self.chat_chain.astream(
                {
                    "text": text,
                    "system_prompt": self.config["models"]["chat"]["system_prompt"],
                    "chat_history": langchain_chat_history,
                }
            ):
                yield chunk
            logger.info(f"聊天回复流式生成完成: {text}")
        except Exception as e:
            logger.error(f"聊天流式调用失败: {e}")
            raise

    async def get_chitchat_response_stream(
        self,
        text: str,
        current_stage: StateNode,
        context: FlowContext,
        flow_description,
    ) -> AsyncGenerator[str, None]:
        """获取闲聊回复 (流式)，使用专门的闲聊模板"""
        try:
            # 加载闲聊模板
            chitchat_template = self.load_prompt_template("chitchat_template")
            prompt = ChatPromptTemplate.from_template(
                chitchat_template, template_format="jinja2"
            )
            chain = prompt | self.chat_model | StrOutputParser()

            # 准备历史对话记录
            history_dialog = []
            for msg in context.get_chat_history():
                history_dialog.append(f"{msg.role.value}: {msg.content}")

            # 准备模板变量
            template_vars = {
                "user_input": text,
                "current_stage": flatten_str(current_stage),
                "history_dialog": "\n".join(history_dialog[-5:]),  # 只使用最近5条记录
                "flow_description": flow_description,
            }

            # 流式生成回复
            async for chunk in chain.astream(template_vars):
                yield chunk

            logger.info(f"闲聊回复流式生成完成: {text}")
        except Exception as e:
            logger.error(f"闲聊流式调用失败: {e}")
            raise

    async def get_llm_choice(
        self,
        prompt_hint: str,
        options: List[str],
        user_input: str,
        context: Optional[FlowContext] = None,
    ) -> str:
        """根据LLM提示语、选项和用户输入，获取LLM的选择结果"""
        template = self.load_prompt_template("llm_choice_template")
        prompt = ChatPromptTemplate.from_template(template)
        chain = prompt | self.chat_model | StrOutputParser()

        try:
            result = await chain.ainvoke(
                {
                    "prompt_hint": prompt_hint,
                    "user_input": user_input,
                    "options": ", ".join(options),
                    "history_dialog": [msg.model_dump() for msg in context.chat_history[-3:]]
                }
            )
            # ... (rest of the logic is the same as before)
            chosen_options = [choice.strip() for choice in result.split(",")]
            valid_choices = [choice for choice in chosen_options if choice in options]

            if not valid_choices:
                if "none" in options:
                    return "none"
                raise ValueError("LLM未能从给定选项中选择有效答案。")

            return valid_choices[0]

        except Exception as e:
            logger.error(f"LLM选择调用失败: {e}")
            raise

    async def extract_data(
        self,
        prompt_hint: str,
        user_input: str,
        required_fields: List[str],
        context: FlowContext = None,
    ) -> Dict[str, Any]:
        """从用户输入中提取结构化数据"""
        template = self.load_prompt_template("parameter_extraction_template")
        prompt = ChatPromptTemplate.from_template(template)
        parser = JsonOutputParser()
        chain = prompt | self.chat_model | parser

        # Create a dynamic Pydantic model for validation
        # 包含原始字段 + missing_fields + followup_message
        if required_fields:
            model_fields = {
                field: (str, Field(..., min_length=1)) for field in required_fields
            }
        else:
            model_fields = {}
        model_fields.update(
            {
                "missing_fields": (List[str], Field(default_factory=list)),
                "followup_message": (str, Field(default="")),
            }
        )
        dynamic_model = create_model("DynamicModel", **model_fields)

        try:
            # 构建上下文信息
            context_info = (
                self._build_context_info(context, required_fields) if context else ""
            )

            result = await chain.ainvoke(
                {
                    "prompt_hint": prompt_hint,
                    "user_input": user_input,
                    "required_fields": ", ".join(required_fields) if required_fields else "",
                    "context_info": context_info,
                    "format_instructions": dynamic_model.model_json_schema(),
                }
            )

            # 验证结果格式
            if not isinstance(result, dict):
                logger.error(f"LLM返回格式错误: {result}")
                raise ValueError("LLM返回的不是有效的JSON格式")

            # 确保关键字段存在
            if "missing_fields" not in result:
                result["missing_fields"] = []
            if "followup_message" not in result:
                result["followup_message"] = ""

            logger.info(f"参数提取完成: {user_input} -> {result}")
            return result

        except Exception as e:
            logger.error(f"参数提取调用失败: {e}")
            raise

    def _build_context_info(
        self, context: FlowContext, required_fields: List[str]
    ) -> str:
        """构建上下文信息字符串，用于LLM参考"""
        context_parts = []

        # 添加个人信息
        if context.personal_info:
            personal_info = context.personal_info
            personal_fields = []
            for field in required_fields:
                if field in personal_info and personal_info[field]:
                    personal_fields.append(f"{field}: {personal_info[field]}")
            if personal_fields:
                context_parts.append("已保存的个人信息：" + "，".join(personal_fields))

        # 添加企业信息
        if context.business_info:
            business_info = context.business_info
            business_fields = []
            for field in required_fields:
                if field in business_info and business_info[field]:
                    business_fields.append(f"{field}: {business_info[field]}")
            if business_fields:
                context_parts.append("已保存的企业信息：" + "，".join(business_fields))

        # 添加许可证信息
        if context.license_info:
            license_info = context.license_info
            license_fields = []
            for field in required_fields:
                if field in license_info and license_info[field]:
                    license_fields.append(f"{field}: {license_info[field]}")
            if license_fields:
                context_parts.append("已保存的许可证信息：" + "，".join(license_fields))

        return "\n".join(context_parts)

    @staticmethod
    def _format_chat_history(
        chat_history: Optional[List[ChatMessage]],
    ) -> List[tuple]:
        """将ChatMessage列表转换为LangChain期望的元组列表"""
        if not chat_history:
            return []

        langchain_history = []
        for msg in chat_history:
            if msg.role == MessageRole.USER:
                langchain_history.append(("user", msg.content))
            elif msg.role == MessageRole.ASSISTANT:
                langchain_history.append(("assistant", msg.content))
            elif msg.role == MessageRole.SYSTEM:
                langchain_history.append(("system", msg.content))
        return langchain_history


# 创建默认的AI引擎实例
default_ai_engine = AIEngine()
