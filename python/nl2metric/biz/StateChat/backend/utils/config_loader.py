"""
配置加载器
"""

import glob
import os
import yaml
from pathlib import Path
from typing import Dict, Any
from loguru import logger

from models.state import FlowConfig, StateNode, InputType
from models.file_validation import FileValidationConfig

# 定义项目根目录
PROJECT_ROOT = Path(__file__).parent.parent


class ConfigLoader:
    """配置文件加载器"""

    @staticmethod
    def load_all_flow_configs(config_dir="config/flow_config/"):
        """加载所有流程配置文件"""
        # 转换为绝对路径
        if not os.path.isabs(config_dir):
            config_dir = PROJECT_ROOT / config_dir

        flow_files = glob.glob(os.path.join(config_dir, "*.yaml"))
        flow_configs = []
        for file in flow_files:
            try:
                cfg = ConfigLoader.load_flow_config(file)
                flow_configs.append(cfg)
            except Exception as e:
                logger.warning(f"加载流程配置失败: {file}, 错误: {e}")
        return flow_configs

    @staticmethod
    def load_flow_config(config_path: str) -> FlowConfig:
        """
        从YAML文件加载流程配置

        Args:
            config_path: 配置文件路径（支持相对路径和绝对路径）

        Returns:
            FlowConfig: 流程配置对象
        """
        try:
            # 转换为绝对路径
            config_file = Path(config_path)
            if not config_file.is_absolute():
                config_file = PROJECT_ROOT / config_path

            if not config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_file}")

            with open(config_file, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)

            logger.info(f"加载配置文件: {config_file}")

            # 转换状态配置
            states = {}
            for state_name, state_data in config_data.get("states", {}).items():
                states[state_name] = ConfigLoader._create_state_node(
                    state_name, state_data
                )

            # 创建流程配置对象
            flow_config = FlowConfig(
                flow_name=config_data.get("flow_name", ""),
                description=config_data.get("description", ""),
                version=config_data.get("version", "1.0.0"),
                states=states,
                initial_state=config_data.get("initial_state", ""),
                final_states=config_data.get("final_states", []),
            )

            logger.info(
                f"配置加载完成: {flow_config.flow_name} v{flow_config.version}, 状态数量: {len(states)}"
            )

            return flow_config

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise

    @staticmethod
    def _create_state_node(state_name: str, state_data: Dict[str, Any]) -> StateNode:
        """创建状态节点对象"""
        # 处理输入类型
        input_type_str = state_data.get("input_type", "text")
        try:
            input_type = InputType(input_type_str)
        except ValueError:
            logger.warning(
                f"未知的输入类型 '{input_type_str}' 在状态 '{state_name}' 中，使用默认类型 'text'"
            )
            input_type = InputType.TEXT

        # 处理文件验证配置
        file_validation = None
        if "file_validation" in state_data:
            file_validation_data = state_data["file_validation"]
            try:
                file_validation = FileValidationConfig(
                    allowed_types=file_validation_data.get("allowed_types", []),
                    max_size_mb=file_validation_data.get("max_size_mb", 10),
                    required_files=file_validation_data.get("required_files", []),
                    naming_convention=file_validation_data.get("naming_convention"),
                    enable_multimodal=file_validation_data.get(
                        "enable_multimodal", False
                    ),
                )
                logger.info(f"状态 '{state_name}' 配置了文件验证")
            except Exception as e:
                logger.error(f"状态 '{state_name}' 的文件验证配置解析失败: {e}")
                validation_result = {
                    "valid": False,
                    "errors": [f"文件验证配置解析失败: {e}"],
                }
                # 可以选择是否抛出异常，这里记录错误并继续
                file_validation = None

        return StateNode(
            name=state_data.get("name", state_name),
            prompt=state_data.get("prompt", ""),
            input_type=input_type,
            options=state_data.get("options"),
            requires=state_data.get("requires"),
            llm_prompt_hint=state_data.get("llm_prompt_hint", ""),
            next=state_data.get("next"),
            file_validation=file_validation,
        )

    @staticmethod
    def validate_config_file(config_path: str) -> Dict[str, Any]:
        """
        验证配置文件的有效性

        Args:
            config_path: 配置文件路径（支持相对路径和绝对路径）

        Returns:
            Dict: 验证结果
        """
        validation_result = {"valid": True, "errors": [], "warnings": []}

        try:
            # 转换为绝对路径
            config_file = Path(config_path)
            if not config_file.is_absolute():
                config_file = PROJECT_ROOT / config_path

            if not config_file.exists():
                validation_result["valid"] = False
                validation_result["errors"].append(f"配置文件不存在: {config_file}")
                return validation_result

            with open(config_file, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)

            # 检查必需字段
            required_fields = ["flow_name", "states", "initial_state", "final_states"]
            for field in required_fields:
                if field not in config_data:
                    validation_result["valid"] = False
                    validation_result["errors"].append(f"缺少必需字段: {field}")

            # 检查状态配置
            states = config_data.get("states", {})
            if not states:
                validation_result["valid"] = False
                validation_result["errors"].append("状态配置为空")

            # 检查初始状态
            initial_state = config_data.get("initial_state")
            if initial_state and initial_state not in states:
                validation_result["valid"] = False
                validation_result["errors"].append(
                    f"初始状态 '{initial_state}' 不存在于状态列表中"
                )

            # 检查终止状态
            final_states = config_data.get("final_states", [])
            for final_state in final_states:
                if final_state not in states:
                    validation_result["valid"] = False
                    validation_result["errors"].append(
                        f"终止状态 '{final_state}' 不存在于状态列表中"
                    )

            # 检查状态转移
            for state_name, state_data in states.items():
                ConfigLoader._validate_state_config(
                    state_name, state_data, states, validation_result
                )

        except yaml.YAMLError as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"YAML格式错误: {e}")
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"验证过程中发生错误: {e}")

        return validation_result

    @staticmethod
    def _validate_state_config(
        state_name: str,
        state_data: Dict[str, Any],
        all_states: Dict[str, Any],
        validation_result: Dict[str, Any],
    ):
        """验证单个状态配置"""
        # 检查必需字段
        required_state_fields = ["name", "prompt", "input_type"]
        for field in required_state_fields:
            if field not in state_data:
                validation_result["errors"].append(
                    f"状态 '{state_name}' 缺少必需字段: {field}"
                )

        # 检查输入类型
        input_type = state_data.get("input_type")
        valid_input_types = [t.value for t in InputType]
        if input_type and input_type not in valid_input_types:
            validation_result["warnings"].append(
                f"状态 '{state_name}' 使用了未知的输入类型: {input_type}"
            )

        # 检查选择项
        if input_type == InputType.CHOICE.value:  # 只检查choice类型
            options = state_data.get("options")
            if not options or not isinstance(options, list):
                validation_result["errors"].append(
                    f"状态 '{state_name}' 的选择类型必须提供选项列表"
                )

        # 检查下一状态
        next_state = state_data.get("next")
        if next_state:
            if isinstance(next_state, str):
                if next_state not in all_states:
                    validation_result["errors"].append(
                        f"状态 '{state_name}' 的下一状态 '{next_state}' 不存在"
                    )
            elif isinstance(next_state, dict):
                for condition, target_state in next_state.items():
                    if target_state and target_state not in all_states:
                        validation_result["errors"].append(
                            f"状态 '{state_name}' 的条件 '{condition}' 对应的目标状态 '{target_state}' 不存在"
                        )

        # 检查文件验证配置
        if "file_validation" in state_data:
            file_validation_data = state_data["file_validation"]

            # 检查文件验证配置的结构
            if not isinstance(file_validation_data, dict):
                validation_result["errors"].append(
                    f"状态 '{state_name}' 的 file_validation 必须是字典类型"
                )
            else:
                # 检查必需的文件验证字段
                required_file_fields = ["allowed_types", "max_size_mb"]
                for field in required_file_fields:
                    if field not in file_validation_data:
                        validation_result["errors"].append(
                            f"状态 '{state_name}' 的 file_validation 缺少必需字段: {field}"
                        )

                # 检查必需文件规则
                if "required_files" in file_validation_data:
                    required_files = file_validation_data["required_files"]
                    if not isinstance(required_files, list):
                        validation_result["errors"].append(
                            f"状态 '{state_name}' 的 required_files 必须是列表类型"
                        )
                    else:
                        for i, rule in enumerate(required_files):
                            if not isinstance(rule, dict):
                                validation_result["errors"].append(
                                    f"状态 '{state_name}' 的 required_files[{i}] 必须是字典类型"
                                )
                            else:
                                rule_fields = ["name_pattern", "description"]
                                for field in rule_fields:
                                    if field not in rule:
                                        validation_result["errors"].append(
                                            f"状态 '{state_name}' 的 required_files[{i}] 缺少字段: {field}"
                                        )

    @staticmethod
    def save_flow_config(flow_config: FlowConfig, config_path: str):
        """
        保存流程配置到YAML文件

        Args:
            flow_config: 流程配置对象
            config_path: 保存路径（支持相对路径和绝对路径）
        """
        try:
            # 转换为字典格式
            config_data = {
                "flow_name": flow_config.flow_name,
                "description": flow_config.description,
                "version": flow_config.version,
                "initial_state": flow_config.initial_state,
                "final_states": flow_config.final_states,
                "states": {},
            }

            # 转换状态配置
            for state_name, state_node in flow_config.states.items():
                state_data = {
                    "name": state_node.name,
                    "prompt": state_node.prompt,
                    "input_type": state_node.input_type.value,
                }

                if state_node.options:
                    state_data["options"] = state_node.options

                if state_node.requires:
                    state_data["requires"] = state_node.requires

                if state_node.llm_prompt_hint:
                    state_data["llm_prompt"] = state_node.llm_prompt_hint

                if state_node.next:
                    state_data["next"] = state_node.next

                config_data["states"][state_name] = state_data

            # 保存到文件，转换为绝对路径
            config_file = Path(config_path)
            if not config_file.is_absolute():
                config_file = PROJECT_ROOT / config_path
            config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(config_file, "w", encoding="utf-8") as f:
                yaml.dump(
                    config_data,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2,
                )

            logger.info(f"配置文件已保存: {config_file}")

        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise
