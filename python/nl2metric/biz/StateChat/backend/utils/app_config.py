"""
应用级配置加载器
使用Pydantic进行类型验证和设置管理
"""

import os
import yaml
from pydantic import BaseModel, Field, validator
from pathlib import Path
from typing import Optional

PROJECT_ROOT = Path(__file__).parent.parent
# 定义配置文件的路径
CONFIG_FILE_PATH = Path(__file__).parent.parent / "config" / "config.yaml"
print(CONFIG_FILE_PATH)


class DatabaseConfig(BaseModel):
    """数据库配置"""

    type: str = Field("sqlite", description="数据库类型")
    database: str = Field("data/state_chat.db", description="数据库名称或文件路径")
    host: Optional[str] = Field(None, description="数据库主机")
    port: Optional[int] = Field(None, description="数据库端口")
    user: Optional[str] = Field(None, description="数据库用户名")
    password: Optional[str] = Field(None, description="数据库密码")
    charset: Optional[str] = Field("utf8mb4", description="字符集")
    echo: bool = Field(False, description="是否打印SQL语句")

    def get_database_url(self) -> str:
        """根据配置生成数据库连接URL"""

        if self.type == "sqlite":
            # SQLite数据库
            db_path = self.database
            if not os.path.isabs(db_path) and db_path != ":memory:":
                # 转换为绝对路径
                absolute_db_path = PROJECT_ROOT / db_path
                # 确保目录存在
                absolute_db_path.parent.mkdir(parents=True, exist_ok=True)
                db_path = str(absolute_db_path)

            return f"sqlite+aiosqlite:///{db_path}"

        elif self.type == "mysql":
            # MySQL数据库
            host = self.host or "localhost"
            port = self.port or 3306
            user = self.user or "root"
            password = self.password or ""
            database = self.database
            charset = self.charset or "utf8mb4"

            return f"mysql+aiomysql://{user}:{password}@{host}:{port}/{database}?charset={charset}"

        elif self.type == "postgresql":
            # PostgreSQL数据库
            host = self.host or "localhost"
            port = self.port or 5432
            user = self.user or "postgres"
            password = self.password or ""
            database = self.database

            return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}"

        else:
            raise ValueError(f"不支持的数据库类型: {self.type}")


class AppSettings(BaseModel):
    """应用核心设置"""

    uploads_dir: str = Field("uploads", description="文件上传的根目录")
    default_flow_name: str = Field("simple_test_flow", description="默认的流程名称")
    host: str = Field("0.0.0.0", description="服务器主机地址")
    port: int = Field(5432, description="服务器端口")
    environment: str = Field("development", description="运行环境")

    @validator("port")
    def resolve_port(cls, v):
        """解析端口，支持环境变量"""
        env_port = os.getenv("PORT")
        if env_port:
            try:
                return int(env_port)
            except ValueError:
                pass
        return v


class APISettings(BaseModel):
    """API及测试客户端配置"""

    base_url: str = Field("http://localhost:5432", description="API服务器的基础URL")
    timeout: int = Field(30, description="API请求超时时间（秒）")
    max_retries: int = Field(3, description="API请求最大重试次数")


class LoggingSettings(BaseModel):
    """日志配置"""

    level: str = Field("INFO", description="日志级别")
    request_logging: bool = Field(True, description="是否记录请求日志")
    response_logging: bool = Field(False, description="是否记录响应日志")
    log_dir: str = Field("logs", description="日志文件路径")


class DatabaseSettings(BaseModel):
    """数据库配置（从复杂配置结构中提取）"""

    development: dict = Field(default_factory=dict)
    test: dict = Field(default_factory=dict)
    production: dict = Field(default_factory=dict)
    migrations: dict = Field(default_factory=dict)

    def get_config_for_environment(self, environment: str) -> DatabaseConfig:
        """根据环境获取数据库配置"""
        env_config = getattr(self, environment, {})
        if not env_config:
            # 如果没有找到对应环境的配置，使用development作为默认
            env_config = self.development

        return DatabaseConfig(**env_config)


class OpenAISettings(BaseModel):
    """OpenAI配置（从复杂配置结构中提取）"""

    api: dict = Field(default_factory=dict)
    models: dict = Field(default_factory=dict)
    rate_limits: dict = Field(default_factory=dict)
    retry: dict = Field(default_factory=dict)
    cache: dict = Field(default_factory=dict)
    prompts: dict = Field(default_factory=dict)


class AppConfig(BaseModel):
    """顶层配置模型"""

    app: AppSettings
    api: APISettings
    logging: LoggingSettings
    # 保持与现有复杂配置的兼容性
    database: Optional[DatabaseSettings] = None
    openai: Optional[OpenAISettings] = None

    def get_database_config(self) -> DatabaseConfig:
        """获取当前环境的数据库配置"""
        if not self.database:
            # 如果没有database配置，使用默认的SQLite配置
            return DatabaseConfig()

        environment = self.app.environment
        return self.database.get_config_for_environment(environment)

    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        db_config = self.get_database_config()
        return db_config.get_database_url()


def load_config() -> AppConfig:
    """
    从YAML文件加载配置并解析到AppConfig模型中
    支持环境变量覆盖
    """
    if not CONFIG_FILE_PATH.is_file():
        raise FileNotFoundError(f"配置文件未找到: {CONFIG_FILE_PATH}")

    with open(CONFIG_FILE_PATH, "r", encoding="utf-8") as f:
        config_data = yaml.safe_load(f)

    # 支持环境变量覆盖配置
    if "app" in config_data:
        # 端口覆盖
        if os.getenv("PORT"):
            try:
                config_data["app"]["port"] = int(os.getenv("PORT"))
            except ValueError:
                pass

        # 主机覆盖
        if os.getenv("HOST"):
            config_data["app"]["host"] = os.getenv("HOST")

        # 日志级别覆盖
        if os.getenv("LOG_LEVEL"):
            config_data["app"]["log_level"] = os.getenv("LOG_LEVEL")

        # 环境覆盖
        if os.getenv("ENVIRONMENT"):
            config_data["app"]["environment"] = os.getenv("ENVIRONMENT")

    return AppConfig(**config_data)


def get_environment() -> str:
    """获取当前运行环境"""
    return os.getenv("ENVIRONMENT", os.getenv("ENV", "development")).lower()


def get_database_config_for_env(config: AppConfig, env: Optional[str] = None) -> dict:
    """
    根据环境获取数据库配置
    这个函数保持与现有复杂数据库配置的兼容性
    """
    if env is None:
        env = config.app.environment

    if config.database and hasattr(config.database, env):
        return getattr(config.database, env, {})

    # 如果没有复杂配置，返回简单的默认配置
    return {
        "type": "sqlite",
        "database": "data/state_chat.db",
        "echo": env == "development",
    }


# 创建一个单例的 config 实例，在应用启动时加载所有配置
# 其他模块可以通过 `from utils.app_config import settings` 来使用
settings: AppConfig = load_config()
