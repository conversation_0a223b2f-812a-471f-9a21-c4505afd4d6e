"""
Mermaid流程图生成器
根据FlowConfig动态生成Mermaid流程图
"""

from typing import Dict, List, Set, Optional, Union
from models.state import FlowConfig, StateNode


class MermaidFlowGenerator:
    """Mermaid流程图生成器"""

    def __init__(self, flow_config: FlowConfig):
        """
        初始化生成器

        Args:
            flow_config: 流程配置对象
        """
        self.flow_config = flow_config
        self.states = flow_config.states
        self.initial_state = flow_config.initial_state
        self.final_states = set(flow_config.final_states)

    def generate_flowchart(
        self, include_details: bool = True, style_theme: str = "default"
    ) -> str:
        """
        生成Mermaid流程图

        Args:
            include_details: 是否包含状态详细信息
            style_theme: 样式主题 (default, colorful, minimal)

        Returns:
            str: Mermaid流程图定义
        """
        lines = []

        # 图表头部
        lines.append("graph TD")
        lines.append(f"    %% {self.flow_config.flow_name} v{self.flow_config.version}")
        lines.append(f"    %% {self.flow_config.description}")
        lines.append("")

        # 添加开始节点
        lines.append(
            "    Start([开始]) --> " + self._format_state_id(self.initial_state)
        )
        lines.append("")

        # 生成状态节点定义
        for state_name, state in self.states.items():
            node_def = self._generate_state_node(state, include_details)
            if node_def:
                lines.append(f"    {node_def}")

        lines.append("")

        # 生成状态转换
        transitions = self._generate_transitions()
        for transition in transitions:
            lines.append(f"    {transition}")

        lines.append("")

        # 添加结束节点连接
        for final_state in self.final_states:
            if final_state in self.states:
                lines.append(
                    f"    {self._format_state_id(final_state)} --> End([结束])"
                )

        lines.append("")

        # 添加样式定义
        if style_theme != "minimal":
            style_lines = self._generate_styles(style_theme)
            lines.extend(style_lines)

        return "\n".join(lines)

    def _format_state_id(self, state_name: str) -> str:
        """格式化状态ID，确保Mermaid兼容性"""
        return state_name.replace("-", "_").replace(" ", "_")

    def _generate_state_node(self, state: StateNode, include_details: bool) -> str:
        """生成状态节点定义"""
        state_id = self._format_state_id(state.name)

        if include_details:
            # 包含详细信息的节点
            details = []
            if state.prompt and len(state.prompt) < 50:
                clean_prompt = self._clean_node_text(state.prompt[:47] + "...")
                details.append(clean_prompt)

            if state.input_type:
                details.append(f"类型: {state.input_type}")

            if state.options:
                options_str = "/".join(state.options[:3])
                if len(state.options) > 3:
                    options_str += "..."
                details.append(f"选项: {options_str}")

            if state.requires:
                requires_str = ",".join(state.requires[:2])
                if len(state.requires) > 2:
                    requires_str += "..."
                details.append(f"必需: {requires_str}")

            detail_text = "<br/>".join(details) if details else state.name
            clean_detail_text = self._clean_node_text(detail_text)
            clean_state_name = self._clean_node_text(state.name)

            # 根据状态类型选择节点形状
            if state.name in self.final_states:
                return f'{state_id}["{clean_state_name}<br/>{clean_detail_text}"]'
            elif isinstance(state.next, dict) and len(state.next) > 1:
                return f'{state_id}{{"{clean_state_name}<br/>{clean_detail_text}"}}'
            else:
                return f'{state_id}["{clean_state_name}<br/>{clean_detail_text}"]'
        else:
            # 简化节点
            clean_state_name = self._clean_node_text(state.name)
            if state.name in self.final_states:
                return f'{state_id}["{clean_state_name}"]'
            elif isinstance(state.next, dict) and len(state.next) > 1:
                return f'{state_id}{{"{clean_state_name}"}}'
            else:
                return f'{state_id}["{clean_state_name}"]'

    def _generate_transitions(self) -> List[str]:
        """生成状态转换定义"""
        transitions = []

        for state_name, state in self.states.items():
            state_id = self._format_state_id(state_name)

            if state.next is None:
                # 终止状态，无转换
                continue
            elif isinstance(state.next, str):
                # 线性转换
                next_id = self._format_state_id(state.next)
                transitions.append(f"{state_id} --> {next_id}")
            elif isinstance(state.next, dict):
                # 条件转换
                for condition, next_state in state.next.items():
                    if next_state:  # 确保目标状态不为空
                        next_id = self._format_state_id(next_state)
                        # 格式化条件标签
                        condition_label = self._format_condition_label(condition)
                        transitions.append(
                            f"{state_id} -->|{condition_label}| {next_id}"
                        )

        return transitions

    def _format_condition_label(self, condition: str) -> str:
        """格式化条件标签"""
        # 处理特殊条件
        if condition == "none":
            return "无/其他"
        elif len(condition) > 10:
            return condition[:8] + "..."
        return condition

    def _clean_state_description(self, description: str) -> str:
        """清理状态描述，确保Mermaid兼容性"""
        if not description:
            return ""

        # 移除换行符和多余空格
        cleaned = description.replace("\n", " ").replace("\r", " ")
        cleaned = " ".join(cleaned.split())  # 移除多余空格

        # 移除或替换特殊字符
        cleaned = cleaned.replace('"', "'")  # 替换双引号为单引号
        cleaned = cleaned.replace(":", "：")  # 替换冒号为中文冒号
        cleaned = cleaned.replace("|", "｜")  # 替换竖线

        # 限制长度
        if len(cleaned) > 80:
            cleaned = cleaned[:77] + "..."

        return cleaned

    def _clean_condition_label(self, condition: str) -> str:
        """清理条件标签，确保Mermaid兼容性"""
        if not condition:
            return ""

        # 处理特殊条件
        if condition == "none":
            return "无/其他"

        # 清理特殊字符
        cleaned = condition.replace('"', "'")
        cleaned = cleaned.replace(":", "：")
        cleaned = cleaned.replace("|", "｜")

        # 限制长度
        if len(cleaned) > 15:
            cleaned = cleaned[:12] + "..."

        return cleaned

    def _clean_node_text(self, text: str) -> str:
        """清理节点文本，确保Mermaid兼容性"""
        if not text:
            return ""

        # 移除换行符和多余空格
        cleaned = text.replace("\n", " ").replace("\r", " ")
        cleaned = " ".join(cleaned.split())  # 移除多余空格

        # 替换特殊字符
        cleaned = cleaned.replace('"', "'")  # 替换双引号为单引号
        cleaned = cleaned.replace("|", "｜")  # 替换竖线
        cleaned = cleaned.replace("<", "＜")  # 替换小于号
        cleaned = cleaned.replace(">", "＞")  # 替换大于号

        return cleaned

    def _generate_styles(self, theme: str) -> List[str]:
        """生成样式定义"""
        lines = ["    %% 样式定义"]

        if theme == "colorful":
            lines.extend(
                [
                    "    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:3px",
                    "    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px",
                    "    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px",
                    "    classDef final fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px",
                    "    classDef collect fill:#fce4ec,stroke:#c2185b,stroke-width:2px",
                ]
            )
        else:  # default theme
            lines.extend(
                [
                    "    classDef startEnd fill:#e3f2fd,stroke:#1976d2,stroke-width:2px",
                    "    classDef process fill:#f5f5f5,stroke:#424242,stroke-width:1px",
                    "    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px",
                    "    classDef final fill:#e8f5e8,stroke:#4caf50,stroke-width:2px",
                ]
            )

        lines.append("")

        # 应用样式到节点
        start_end_nodes = ["Start", "End"]
        decision_nodes = []
        final_nodes = []
        process_nodes = []
        collect_nodes = []

        for state_name, state in self.states.items():
            state_id = self._format_state_id(state_name)

            if state_name in self.final_states:
                final_nodes.append(state_id)
            elif isinstance(state.next, dict) and len(state.next) > 1:
                decision_nodes.append(state_id)
            elif "collect" in state_name.lower() or "info" in state_name.lower():
                collect_nodes.append(state_id)
            else:
                process_nodes.append(state_id)

        # 应用样式类
        if start_end_nodes:
            lines.append(f"    class {','.join(start_end_nodes)} startEnd")
        if process_nodes:
            lines.append(f"    class {','.join(process_nodes)} process")
        if decision_nodes:
            lines.append(f"    class {','.join(decision_nodes)} decision")
        if final_nodes:
            lines.append(f"    class {','.join(final_nodes)} final")
        if collect_nodes and theme == "colorful":
            lines.append(f"    class {','.join(collect_nodes)} collect")

        return lines

    def generate_state_diagram(self) -> str:
        """生成状态图（state diagram）"""
        lines = []
        lines.append("stateDiagram-v2")
        lines.append(f"    %% {self.flow_config.flow_name}")
        lines.append("")

        # 添加状态定义 - 修复语法错误
        for state_name, state in self.states.items():
            state_id = self._format_state_id(state_name)
            if state.prompt and len(state.prompt) > 0:
                # 清理状态描述，移除换行符和特殊字符
                description = self._clean_state_description(state.prompt)
                lines.append(f"    {state_id} : {description}")
            else:
                lines.append(f"    {state_id} : {state.name}")

        lines.append("")

        # 添加初始状态转换
        initial_state_id = self._format_state_id(self.initial_state)
        lines.append(f"    [*] --> {initial_state_id}")

        # 添加状态转换
        for state_name, state in self.states.items():
            state_id = self._format_state_id(state_name)

            if state.next is None:
                # 终止状态
                if state_name in self.final_states:
                    lines.append(f"    {state_id} --> [*]")
            elif isinstance(state.next, str):
                # 线性转换
                next_state_id = self._format_state_id(state.next)
                lines.append(f"    {state_id} --> {next_state_id}")
            elif isinstance(state.next, dict):
                # 条件转换
                for condition, next_state in state.next.items():
                    if next_state:
                        next_state_id = self._format_state_id(next_state)
                        # 清理条件标签
                        clean_condition = self._clean_condition_label(condition)
                        lines.append(
                            f"    {state_id} --> {next_state_id} : {clean_condition}"
                        )

        return "\n".join(lines)

    def get_flow_statistics(self) -> Dict:
        """获取流程统计信息"""
        decision_states = 0
        linear_states = 0
        final_states = len(self.final_states)

        for state in self.states.values():
            if isinstance(state.next, dict) and len(state.next) > 1:
                decision_states += 1
            elif isinstance(state.next, str):
                linear_states += 1

        return {
            "total_states": len(self.states),
            "decision_states": decision_states,
            "linear_states": linear_states,
            "final_states": final_states,
            "initial_state": self.initial_state,
            "flow_name": self.flow_config.flow_name,
            "version": self.flow_config.version,
            "description": self.flow_config.description,
        }
