"""
工具类包
"""

from pydantic import BaseModel
import yaml
from enum import Enum
from typing import Any, Dict, List


def _model_to_dict_with_descriptions(
    model_instance: Any, model_fields: Dict[str, Any]
) -> Any:
    """
    递归地将 Pydantic 模型实例及其嵌套结构转换为可序列化的字典，使用字段描述作为键。
    """
    if isinstance(model_instance, BaseModel):
        result_dict = {}
        # Use the passed model_fields for the current model instance
        for field_name, field_info in model_fields.items():
            value = getattr(model_instance, field_name, None)
            description = field_info.description or field_name
            # For nested values, get their model_fields if they are also BaseModel
            nested_model_fields = (
                value.__class__.model_fields if isinstance(value, BaseModel) else {}
            )
            result_dict[description] = _model_to_dict_with_descriptions(
                value, nested_model_fields
            )
        return result_dict
    elif isinstance(model_instance, list):
        # For a list, assume items are Pydantic models and get their model_fields
        return [
            _model_to_dict_with_descriptions(
                item, item.__class__.model_fields if isinstance(item, BaseModel) else {}
            )
            for item in model_instance
        ]
    elif isinstance(model_instance, dict):
        # For a dict, check if values are Pydantic models
        result_dict = {}
        for key, value in model_instance.items():
            if isinstance(value, BaseModel):
                result_dict[key] = _model_to_dict_with_descriptions(
                    value, value.__class__.model_fields
                )
            else:
                result_dict[key] = _model_to_dict_with_descriptions(value, {})
        return result_dict
    elif isinstance(model_instance, Enum):
        return model_instance.value
    else:
        return model_instance


def flatten_str(model: BaseModel) -> str:
    """将状态所有字段描述：值，转换为YAML字符串表示"""
    if model is None:
        return "None"

    data_to_dump = {}
    # 获取字段信息，使用 model.__class__.model_fields 来避免弃用警告
    model_fields = model.__class__.model_fields
    for field_name, field_info in model_fields.items():
        # 获取字段值
        value = getattr(model, field_name)

        # 获取字段描述（来自Field的description）
        description = field_info.description or field_name

        # Determine the model_fields to pass for the current value
        nested_model_fields = {}
        if isinstance(value, BaseModel):
            nested_model_fields = value.__class__.model_fields
        elif isinstance(value, list) and value and isinstance(value[0], BaseModel):
            # If it's a non-empty list of models, get fields from the first item
            nested_model_fields = value[0].__class__.model_fields

        processed_value = _model_to_dict_with_descriptions(value, nested_model_fields)

        data_to_dump[description] = processed_value

    return yaml.safe_dump(
        data_to_dump, allow_unicode=True, sort_keys=False, default_flow_style=False
    )
