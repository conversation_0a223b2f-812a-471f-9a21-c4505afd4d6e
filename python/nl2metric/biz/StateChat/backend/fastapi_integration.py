"""
FastAPI集成模块
"""

from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import json
from datetime import datetime

from langgraph_chat_flow import StateChatManager
from enhanced_llm_processor import EnhancedLLMProcessor
from file_validator import FileValidator, FileValidationConfig
from test_enhanced_chat import EnhancedStateChatManager

app = FastAPI(title="State Chat API", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
chat_manager = None
llm_processor = None


class MessageRequest(BaseModel):
    """消息请求模型"""

    session_id: str
    message: str
    files: Optional[List[Dict[str, Any]]] = None


class FileUploadRequest(BaseModel):
    """文件上传请求模型"""

    session_id: str
    validation_config: Dict[str, Any]


class ChatResponse(BaseModel):
    """聊天响应模型"""

    response: str
    current_state: str
    user_data: Dict[str, Any]
    is_complete: bool
    missing_fields: Optional[List[str]] = None
    validation_errors: Optional[Dict[str, Any]] = None


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global chat_manager, llm_processor
    chat_manager = EnhancedStateChatManager("config/flow_config/simple_test_flow.yaml")
    llm_processor = EnhancedLLMProcessor()
    print("State Chat API started successfully")


@app.post("/chat/message", response_model=ChatResponse)
async def handle_message(request: MessageRequest):
    """处理聊天消息"""
    try:
        result = await chat_manager.handle_message_with_enhancement(
            session_id=request.session_id, message=request.message, files=request.files
        )

        return ChatResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/chat/upload")
async def upload_files(
    session_id: str = Form(...),
    files: List[UploadFile] = File(...),
    validation_config: str = Form(...),
):
    """上传文件"""
    try:
        # 解析验证配置
        config_dict = json.loads(validation_config)

        # 处理上传的文件
        processed_files = []
        for file in files:
            file_info = {
                "filename": file.filename,
                "content_type": file.content_type,
                "size": await file.read(),  # 这里应该保存文件内容
            }
            processed_files.append(file_info)
            await file.seek(0)  # 重置文件指针

        # 验证文件
        result = await chat_manager.validate_and_process_files(
            session_id=session_id, files=processed_files, validation_config=config_dict
        )

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/chat/session/{session_id}")
async def get_session_info(session_id: str):
    """获取会话信息"""
    try:
        session_info = chat_manager.get_session_info(session_id)
        return session_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/chat/session/{session_id}")
async def reset_session(session_id: str):
    """重置会话"""
    try:
        chat_manager.reset_session(session_id)
        return {"message": "Session reset successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now()}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=18000)
