"""
修正的测试版本
"""

import asyncio
from test_mock_chat import MockChatManager


async def test_corrected_flow():
    """测试修正后的流程"""
    print("=== 测试修正的智能客服流程 ===\n")

    # 初始化管理器
    manager = MockChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"test_user_corrected"

    # 模拟对话流程 - 使用正确的选项值
    test_scenarios = [
        {
            "message": "你好",
            "expected_state": "check_registration",
            "description": "欢迎用户",
        },
        {
            "message": "否",  # 使用配置文件中的确切值
            "expected_state": "register",
            "description": "用户选择未注册",
        },
        {
            "message": "个人",  # 使用配置文件中的确切值
            "expected_state": "collect_personal_info",
            "description": "选择个人注册",
        },
        {
            "message": "我叫李四，我的邮箱是****************，手机号是13900139000",
            "expected_state": "main_process",
            "description": "提供完整个人信息",
        },
        {
            "message": "服务A",  # 使用配置文件中的确切值
            "expected_state": "service_a",
            "description": "选择服务A",
        },
    ]

    for i, scenario in enumerate(test_scenarios, 1):
        print(f"--- 步骤 {i}: {scenario['description']} ---")
        print(f"用户输入: {scenario['message']}")

        result = await manager.handle_message(session_id, scenario["message"])

        print(f"AI回复: {result['response']}")
        print(f"当前状态: {result['current_state']}")
        print(f"收集的数据: {result['user_data']}")
        print(f"预期状态: {scenario['expected_state']}")
        print(
            f"状态匹配: {'✓' if result['current_state'] == scenario['expected_state'] else '✗'}"
        )
        print(f"是否完成: {result['is_complete']}")
        print("\n" + "=" * 50 + "\n")

        # 短暂延迟
        await asyncio.sleep(0.5)

    # 获取会话总结
    session_info = manager.get_session_info(session_id)
    print("=== 会话总结 ===")
    print(f"会话ID: {session_id}")
    print(f"当前状态: {session_info.get('current_state')}")
    print(f"用户数据: {session_info.get('user_data')}")
    print(f"消息数量: {session_info.get('message_count')}")

    # 打印完整对话历史
    print("\n=== 完整对话历史 ===")
    state = manager.sessions[session_id]
    for msg in state.messages:
        print(f"{msg['role']}: {msg['content']}")


if __name__ == "__main__":
    asyncio.run(test_corrected_flow())
