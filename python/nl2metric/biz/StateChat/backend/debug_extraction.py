"""
调试版本
"""

import asyncio
from test_mock_chat import MockChatManager


async def debug_extraction():
    """调试数据提取"""
    manager = MockChatManager("config/flow_config/simple_test_flow.yaml")
    flow = manager.flow

    # 测试数据提取
    text = "我叫李四，我的邮箱是****************，手机号是13900139000"
    required_fields = ["name", "email", "phone"]

    extracted = flow._extract_fields(text, required_fields)
    print("输入文本:", text)
    print("提取结果:", extracted)
    print("缺失字段:", [f for f in required_fields if not extracted.get(f)])


if __name__ == "__main__":
    asyncio.run(debug_extraction())
