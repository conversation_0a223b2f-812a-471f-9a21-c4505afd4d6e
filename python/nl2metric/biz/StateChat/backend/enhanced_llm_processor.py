"""
增强的LLM集成模块
"""

import json
import re
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field


class ExtractedData(BaseModel):
    """提取的数据模型"""

    name: Optional[str] = Field(None, description="姓名")
    email: Optional[str] = Field(None, description="邮箱地址")
    phone: Optional[str] = Field(None, description="电话号码")
    company_name: Optional[str] = Field(None, description="公司名称")
    business_type: Optional[str] = Field(None, description="业务类型")
    contact_email: Optional[str] = Field(None, description="联系人邮箱")
    missing_fields: List[str] = Field(default_factory=list, description="缺失的字段")


class IntentClassifier(BaseModel):
    """意图分类结果"""

    intent: str = Field(description="用户意图")
    confidence: float = Field(description="置信度")
    entities: Dict[str, str] = Field(default_factory=dict, description="提取的实体")


class EnhancedLLMProcessor:
    """增强的LLM处理器"""

    def __init__(self, model_name: str = "qwen3_32b", temperature: float = 0.1):
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=temperature,
            api_key="xxx",
            base_url="http://**************:16701/v1",
            streaming=False,
        )
        self.data_parser = PydanticOutputParser(pydantic_object=ExtractedData)
        self.intent_parser = PydanticOutputParser(pydantic_object=IntentClassifier)

    async def extract_structured_data(
        self, text: str, required_fields: List[str], prompt_hint: str = None
    ) -> ExtractedData:
        """提取结构化数据"""
        prompt = f"""
        请从以下文本中提取结构化信息。
        
        必需字段: {", ".join(required_fields)}
        
        提取规则:
        - 姓名: 中文或英文全名
        - 邮箱: 标准邮箱格式 (<EMAIL>)
        - 电话: 11位手机号或带区号的固定电话
        - 公司名称: 包含'公司'、'企业'等关键词
        - 业务类型: 简短的业务描述
        
        用户输入: {text}
        
        {prompt_hint or ""}
        
        {self.data_parser.get_format_instructions()}
        """

        response = await self.llm.ainvoke(
            [SystemMessage(content=prompt), HumanMessage(content=text)]
        )

        try:
            # 尝试解析JSON响应
            return self.data_parser.parse(response.content)
        except:
            # 如果解析失败，尝试从文本中提取
            return self._fallback_extraction(text, required_fields)

    def _fallback_extraction(
        self, text: str, required_fields: List[str]
    ) -> ExtractedData:
        """备用提取方法"""
        data = ExtractedData()

        # 提取邮箱
        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        email_match = re.search(email_pattern, text)
        if email_match:
            data.email = email_match.group()

        # 提取手机号
        phone_pattern = r"1[3-9]\d{9}"
        phone_match = re.search(phone_pattern, text)
        if phone_match:
            data.phone = phone_match.group()

        # 提取姓名（简单模式）
        name_patterns = [
            r"我叫([^，。\s]+)",
            r"姓名[：:]\s*([^，。\s]+)",
            r"是([^，。\s]+)",
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text)
            if name_match and len(name_match.group(1)) >= 2:
                data.name = name_match.group(1)
                break

        # 检查缺失字段
        for field in required_fields:
            if not getattr(data, field, None):
                data.missing_fields.append(field)

        return data

    async def classify_intent(self, text: str, options: List[str]) -> IntentClassifier:
        """分类用户意图"""
        prompt = f"""
        请分析用户的输入，判断其意图。
        
        可选选项: {", ".join(options)}
        
        请返回最匹配的选项和置信度。
        
        {self.intent_parser.get_format_instructions()}
        
        用户输入: {text}
        """

        response = await self.llm.ainvoke(
            [SystemMessage(content=prompt), HumanMessage(content=text)]
        )

        try:
            return self.intent_parser.parse(response.content)
        except:
            # 如果解析失败，进行简单匹配
            return self._fallback_intent_classification(text, options)

    def _fallback_intent_classification(
        self, text: str, options: List[str]
    ) -> IntentClassifier:
        """备用意图分类"""
        text_lower = text.lower()

        for option in options:
            if option.lower() in text_lower:
                return IntentClassifier(intent=option, confidence=0.8)

        # 如果没有匹配，返回第一个选项
        return IntentClassifier(
            intent=options[0] if options else "unknown", confidence=0.3
        )

    async def generate_follow_up_question(
        self, missing_fields: List[str], context: str = ""
    ) -> str:
        """生成追问问题"""
        if not missing_fields:
            return ""

        prompt = f"""
        用户正在提供信息，但缺少以下字段: {", ".join(missing_fields)}
        
        上下文: {context}
        
        请生成一个自然友好的追问，引导用户补充缺失信息。
        要求:
        1. 语气友好
        2. 明确指出需要什么信息
        3. 可以给出示例
        4. 只生成一个问题
        """

        response = await self.llm.ainvoke(
            [
                SystemMessage(content=prompt),
                HumanMessage(content=f"缺失字段: {missing_fields}"),
            ]
        )

        return response.content.strip()

    async def validate_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据质量"""
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": [],
        }

        # 验证邮箱格式
        if data.get("email"):
            email_pattern = r"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$"
            if not re.match(email_pattern, data["email"]):
                validation_results["errors"].append("邮箱格式不正确")

        # 验证手机号格式
        if data.get("phone"):
            phone_pattern = r"^1[3-9]\d{9}$"
            if not re.match(phone_pattern, data["phone"]):
                validation_results["errors"].append("手机号格式不正确")

        # 验证姓名
        if data.get("name"):
            name = data["name"]
            if len(name) < 2:
                validation_results["warnings"].append("姓名可能过短")
            if len(name) > 50:
                validation_results["warnings"].append("姓名过长")

        return validation_results

    async def summarize_conversation(
        self, messages: List[Union[HumanMessage, AIMessage]]
    ) -> str:
        """总结对话"""
        conversation_text = "\n".join(
            [
                f"{'用户' if isinstance(msg, HumanMessage) else 'AI'}: {msg.content}"
                for msg in messages[-10:]  # 只总结最近10条消息
            ]
        )

        prompt = f"""
        请总结以下对话的主要内容，提取关键信息。
        
        对话内容:
        {conversation_text}
        
        总结要求:
        1. 提取用户的主要需求
        2. 列出已收集的关键信息
        3. 指出当前的处理进度
        """

        response = await self.llm.ainvoke(
            [SystemMessage(content=prompt), HumanMessage(content="请总结这段对话")]
        )

        return response.content.strip()
