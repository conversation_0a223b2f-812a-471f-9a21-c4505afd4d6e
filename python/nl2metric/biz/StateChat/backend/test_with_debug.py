"""
带调试信息的测试版本
"""

import asyncio
from test_mock_chat import MockChatManager


async def test_with_debug():
    """带调试信息的测试"""
    print("=== 带调试的智能客服流程测试 ===\n")

    # 初始化管理器
    manager = MockChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"test_debug"

    # 测试到个人信息收集步骤
    steps = [
        ("你好", "欢迎"),
        ("否", "选择未注册"),
        ("个人", "选择个人注册"),
        ("我叫李四，我的邮箱是****************，手机号是13900139000", "提供个人信息"),
    ]

    for i, (message, desc) in enumerate(steps, 1):
        print(f"--- 步骤 {i}: {desc} ---")
        print(f"用户输入: {message}")

        result = await manager.handle_message(session_id, message)

        print(f"AI回复: {result['response']}")
        print(f"当前状态: {result['current_state']}")
        print(f"收集的数据: {result['user_data']}")

        # 检查是否在个人信息收集状态
        if result["current_state"] == "collect_personal_info":
            state = manager.sessions[session_id]
            config = manager.flow.states["collect_personal_info"]
            print(f"当前状态配置 requires: {config.get('requires', [])}")

            # 手动测试提取
            extracted = manager.flow._extract_fields(
                message, config.get("requires", [])
            )
            print(f"手动提取结果: {extracted}")

        print("\n" + "=" * 50 + "\n")

        await asyncio.sleep(0.5)


if __name__ == "__main__":
    asyncio.run(test_with_debug())
