"""
使用提示模板的聊天管理器 - Mock LLM版本
"""

import asyncio
import json
import re
import yaml
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from jinja2 import Template
from pydantic import BaseModel, Field


class ExtractedData(BaseModel):
    """提取的数据模型"""

    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    company_name: Optional[str] = None
    business_type: Optional[str] = None
    contact_email: Optional[str] = None
    missing_fields: List[str] = []
    follow_up_message: str = ""


class MockLLMClient:
    """模拟LLM客户端"""

    async def chat_completions_create(
        self, model: str, messages: List[Dict], temperature: float = 0.1
    ):
        """模拟LLM调用"""

        class MockChoice:
            def __init__(self, content):
                self.message = type("Message", (), {"content": content})()

        class MockResponse:
            def __init__(self, choices):
                self.choices = choices

        user_message = messages[0]["content"]

        # 根据不同的提示类型返回不同的模拟响应
        if "请严格按照以下步骤进行" in user_message:
            # 选择处理
            if "个人" in user_message or "注册个人账号" in user_message:
                return MockResponse([MockChoice("个人")])
            elif "企业" in user_message or "公司" in user_message:
                return MockResponse([MockChoice("企业")])
            elif "服务A" in user_message:
                return MockResponse([MockChoice("服务A")])
            elif "服务B" in user_message:
                return MockResponse([MockChoice("服务B")])
            elif "服务C" in user_message:
                return MockResponse([MockChoice("服务C")])
            elif "是" in user_message or "已经注册" in user_message:
                return MockResponse([MockChoice("是")])
            elif "否" in user_message or "没有注册" in user_message:
                return MockResponse([MockChoice("否")])

        elif "你是一个智能助手" in user_message:
            # 参数提取
            text_match = re.search(r"用户输入: (.+)", user_message)
            if text_match:
                user_text = text_match.group(1)

                # 模拟提取结果
                result = {}
                missing = []

                # 提取邮箱
                email_match = re.search(
                    r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}", user_text
                )
                if email_match:
                    result["email"] = email_match.group()
                else:
                    missing.append("email")

                # 提取手机号
                phone_match = re.search(r"1[3-9]\d{9}", user_text)
                if phone_match:
                    result["phone"] = phone_match.group()
                else:
                    missing.append("phone")

                # 提取姓名
                name_match = re.search(
                    r"我叫(.+?)，|姓名[：:](.+?)，|姓名[：:](.+?)[。\s]", user_text
                )
                if name_match:
                    result["name"] = (
                        name_match.group(1)
                        or name_match.group(2)
                        or name_match.group(3)
                    )
                else:
                    missing.append("name")

                # 生成追问
                follow_up = ""
                if missing:
                    field_names = {"name": "姓名", "email": "邮箱", "phone": "手机号"}
                    follow_up = f"请补充以下信息：{', '.join(field_names.get(f, f) for f in missing)}"

                # 构建JSON响应
                json_result = {
                    **{k: v for k, v in result.items() if v},
                    "missing_fields": missing,
                    "follow_up_message": follow_up,
                }

                return MockResponse(
                    [MockChoice(json.dumps(json_result, ensure_ascii=False))]
                )

        # 默认返回
        return MockResponse([MockChoice("{}")])


class EnhancedChatState:
    """增强聊天状态"""

    def __init__(self, current_state: str):
        self.current_state = current_state
        self.messages = []
        self.user_data = {}
        self.is_complete = False
        self.context_info = {}  # 保存上下文信息


class EnhancedStateChatFlow:
    """增强状态聊天流程 - 集成LLM提示模板"""

    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.states = self.config["states"]
        self.prompts_dir = "prompts"
        self._load_prompt_templates()

        # 使用模拟LLM
        self.llm_client = MockLLMClient()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载流程配置"""
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    def _load_prompt_templates(self):
        """加载提示模板"""
        self.intent_template = self._load_template("intent_template.jinja2")
        self.param_template = self._load_template(
            "parameter_extraction_template.jinja2"
        )
        self.choice_template = self._load_template("llm_choice_template.jinja2")

    def _load_template(self, filename: str) -> Template:
        """加载Jinja2模板"""
        path = os.path.join(self.prompts_dir, filename)
        with open(path, "r", encoding="utf-8") as f:
            return Template(f.read())

    async def process_message(
        self, state: EnhancedChatState, message: str
    ) -> EnhancedChatState:
        """处理用户消息"""
        # 添加用户消息
        state.messages.append({"role": "user", "content": message})

        # 获取当前状态配置
        current_config = self.states.get(state.current_state)
        if not current_config:
            state.messages.append({"role": "assistant", "content": "错误：无效的状态"})
            return state

        # 根据输入类型处理
        if current_config["input_type"] == "choice":
            next_state = await self._process_choice_with_llm(
                message, current_config, state
            )
            if not next_state:
                # 没有匹配的选择，提示用户
                options = current_config.get("options", [])
                state.messages.append(
                    {
                        "role": "assistant",
                        "content": f"请选择有效选项：{', '.join(options)}",
                    }
                )
                return state
        elif current_config["input_type"] == "text":
            next_state = await self._process_text_with_llm(
                state, message, current_config
            )
        else:
            next_state = current_config.get("next")

        # 更新状态
        if next_state:
            state.current_state = next_state
            next_config = self.states.get(next_state)

            if next_config:
                # 添加AI响应
                ai_response = next_config["prompt"]
                state.messages.append({"role": "assistant", "content": ai_response})

                # 检查是否是终止状态
                if next_state in self.config.get("final_states", []):
                    state.is_complete = True

        return state

    async def _process_choice_with_llm(
        self, message: str, config: Dict[str, Any], state: EnhancedChatState
    ) -> Optional[str]:
        """使用LLM处理选择输入"""
        options = config.get("options", [])
        next_mapping = config.get("next", {})
        prompt_hint = config.get("llm_prompt_hint", "")

        # 构建提示
        prompt = self.choice_template.render(
            prompt_hint=prompt_hint, user_input=message, options=options
        )

        try:
            # 调用LLM
            response = await self.llm_client.chat_completions_create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
            )

            choice = response.choices[0].message.content.strip()

            # 查找匹配的选项
            for option in options:
                if option.lower() in choice.lower() or choice.lower() in option.lower():
                    return next_mapping.get(option)

        except Exception as e:
            print(f"LLM调用失败，使用回退方案: {e}")
            # 回退到简单匹配
            return self._fallback_choice_match(message, config)

        return None

    def _fallback_choice_match(
        self, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """回退的选择匹配"""
        options = config.get("options", [])
        next_mapping = config.get("next", {})

        message_lower = message.lower()
        for option in options:
            if option.lower() in message_lower:
                return next_mapping.get(option)

        return None

    async def _process_text_with_llm(
        self, state: EnhancedChatState, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """使用LLM处理文本输入"""
        next_state = config.get("next")

        # 如果需要提取字段
        if "requires" in config:
            required_fields = config["requires"]
            prompt_hint = config.get("llm_prompt_hint", "")

            # 使用LLM提取数据
            extracted_data = await self._extract_fields_with_llm(
                message, required_fields, prompt_hint, state.context_info
            )

            # 检查是否所有必需字段都已提取
            if extracted_data.missing_fields:
                # 生成追问
                follow_up = extracted_data.follow_up_message
                state.messages.append({"role": "assistant", "content": follow_up})

                # 保存已提取的数据到上下文
                for field in required_fields:
                    value = getattr(extracted_data, field)
                    if value:
                        state.context_info[field] = value
                        state.user_data[field] = value

                return None  # 保持当前状态
            else:
                # 所有字段都已提供，保存数据
                for field in required_fields:
                    value = getattr(extracted_data, field)
                    if value:
                        state.user_data[field] = value
                        state.context_info[field] = value

        return next_state

    async def _extract_fields_with_llm(
        self,
        text: str,
        required_fields: List[str],
        prompt_hint: str,
        context_info: Dict[str, Any],
    ) -> ExtractedData:
        """使用LLM提取字段"""

        # 构建格式说明
        format_example = {
            "name": "张三",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "missing_fields": [],
            "follow_up_message": "",
        }

        # 构建提示
        prompt = self.param_template.render(
            context_info=json.dumps(context_info, ensure_ascii=False),
            prompt_hint=prompt_hint,
            required_fields=required_fields,
            user_input=text,
            format_instructions=json.dumps(format_example, ensure_ascii=False),
        )

        try:
            # 调用LLM
            response = await self.llm_client.chat_completions_create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
            )

            # 解析响应
            result_text = response.choices[0].message.content.strip()

            # 尝试解析JSON
            result_dict = json.loads(result_text)

            # 创建ExtractedData对象
            extracted = ExtractedData()
            for field in required_fields:
                if field in result_dict:
                    setattr(extracted, field, result_dict[field])

            extracted.missing_fields = result_dict.get("missing_fields", [])
            extracted.follow_up_message = result_dict.get("follow_up_message", "")

            return extracted

        except Exception as e:
            print(f"LLM提取失败，使用回退方案: {e}")
            # 回退到正则表达式提取
            return self._fallback_extract_fields(text, required_fields)

    def _fallback_extract_fields(
        self, text: str, required_fields: List[str]
    ) -> ExtractedData:
        """回退的字段提取"""
        extracted = ExtractedData()

        # 提取邮箱
        email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
        email_match = re.search(email_pattern, text)
        if email_match:
            extracted.email = email_match.group()

        # 提取手机号
        phone_pattern = r"1[3-9]\d{9}"
        phone_match = re.search(phone_pattern, text)
        if phone_match:
            extracted.phone = phone_match.group()

        # 提取姓名
        if "name" in required_fields:
            name_patterns = [r"我叫([^，。\s]+)", r"姓名[：:]\s*([^，。\s]+)"]
            for pattern in name_patterns:
                name_match = re.search(pattern, text)
                if name_match and len(name_match.group(1)) >= 2:
                    extracted.name = name_match.group(1)
                    break

        # 检查缺失字段
        for field in required_fields:
            value = getattr(extracted, field)
            if not value:
                extracted.missing_fields.append(field)

        return extracted


class EnhancedChatManager:
    """增强聊天管理器"""

    def __init__(self, config_path: str):
        self.flow = EnhancedStateChatFlow(config_path)
        self.sessions: Dict[str, EnhancedChatState] = {}

    async def handle_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        # 获取或创建会话
        if session_id not in self.sessions:
            self.sessions[session_id] = EnhancedChatState(
                current_state=self.flow.config["initial_state"]
            )

            # 添加欢迎消息
            initial_config = self.flow.states[self.flow.config["initial_state"]]
            self.sessions[session_id].messages.append(
                {"role": "assistant", "content": initial_config["prompt"]}
            )

        state = self.sessions[session_id]

        # 处理消息
        new_state = await self.flow.process_message(state, message)
        self.sessions[session_id] = new_state

        # 返回最新响应
        latest_message = new_state.messages[-1] if new_state.messages else {}

        return {
            "response": latest_message.get("content", ""),
            "current_state": new_state.current_state,
            "user_data": new_state.user_data,
            "is_complete": new_state.is_complete,
            "messages": new_state.messages,
        }

    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        if session_id in self.sessions:
            state = self.sessions[session_id]
            return {
                "current_state": state.current_state,
                "user_data": state.user_data,
                "message_count": len(state.messages),
                "is_complete": state.is_complete,
            }
        return {}

    def reset_session(self, session_id: str):
        """重置会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]


async def test_enhanced_with_prompts():
    """测试使用提示模板的增强版流程"""
    print("=== 测试使用提示模板的智能客服流程 ===\n")

    # 初始化管理器
    manager = EnhancedChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"test_prompts_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # 完整的对话流程
    conversation = [
        "你好",
        "我还没有注册",
        "我想注册个人账号",
        "我叫王小明，邮箱是************************",
        "我的手机号是13812345678",
        "我想办理服务A",
    ]

    for i, message in enumerate(conversation, 1):
        print(f"【第{i}轮对话】")
        print(f"用户：{message}")

        result = await manager.handle_message(session_id, message)

        print(f"\n客服：{result['response']}")
        print(f"[当前状态：{result['current_state']}]")

        if result["user_data"]:
            print(
                f"[已收集信息：{', '.join(f'{k}={v}' for k, v in result['user_data'].items())}]"
            )

        print("-" * 60)

        await asyncio.sleep(0.5)

    # 显示最终会话状态
    session_info = manager.get_session_info(session_id)
    print(f"\n=== 会话总结 ===")
    print(f"会话ID：{session_id}")
    print(f"最终状态：{session_info['current_state']}")
    print(f"是否完成：{session_info['is_complete']}")
    print(f"收集的用户数据：{session_info['user_data']}")
    print(f"总消息数：{session_info['message_count']}")


if __name__ == "__main__":
    asyncio.run(test_enhanced_with_prompts())
