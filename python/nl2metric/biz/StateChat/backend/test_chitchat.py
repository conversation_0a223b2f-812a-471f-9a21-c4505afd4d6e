#!/usr/bin/env python3
"""
测试闲聊功能的脚本
"""

import asyncio
import json
from utils.ai_engine import default_ai_engine
from models.context import FlowContext, MessageRole, ChatMessage
from models.state import StateNode, InputType
from datetime import datetime


async def test_chitchat():
    """测试闲聊功能"""
    print("开始测试闲聊功能...")

    # 创建测试数据
    user_input = "今天天气真不错啊！"

    # 创建模拟的当前状态
    current_state = StateNode(
        name="welcome_user", prompt="欢迎使用政务服务系统", input_type=InputType.TEXT
    )

    # 创建模拟的上下文
    context = FlowContext(session_id="test_session_001", flow_name="test_flow")

    # 添加一些历史对话
    context.add_chat_message(MessageRole.USER, "你好，我想办理营业执照")
    context.add_chat_message(
        MessageRole.ASSISTANT, "您好！我可以帮您办理营业执照。请问您是个体户还是企业？"
    )

    print(f"用户输入: {user_input}")
    print(f"当前状态: {current_state.name}")
    print(f"历史对话记录: {len(context.get_chat_history())} 条")

    try:
        # 测试闲聊功能
        print("\n=== 闲聊回复 ===")
        full_response = ""
        async for chunk in default_ai_engine.get_chitchat_response_stream(
            user_input, current_state, context
        ):
            print(chunk, end="", flush=True)
            full_response += chunk

        print(f"\n\n完整回复: {full_response}")

        # 验证回复是否包含期望的内容
        expected_keywords = ["天气", "业务", "办理", "政务服务"]
        found_keywords = [kw for kw in expected_keywords if kw in full_response]

        print(f"\n找到的关键词: {found_keywords}")

        if len(found_keywords) >= 2:
            print("✅ 闲聊功能测试通过！")
        else:
            print("❌ 闲聊功能可能需要改进")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_chitchat())
