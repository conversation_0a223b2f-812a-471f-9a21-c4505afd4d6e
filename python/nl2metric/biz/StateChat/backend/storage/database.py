"""
数据库管理模块
"""

from sqlalchemy.ext.asyncio import (
    create_async_engine,
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
)
from loguru import logger
from typing import Optional
from utils.app_config import settings
from storage.models import Base
from sqlalchemy import create_engine
import asyncio


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        """
        初始化数据库管理器
        """
        self.engine: Optional[AsyncEngine] = None
        self.sessionmaker: Optional[async_sessionmaker[AsyncSession]] = None

    async def init(self):
        """初始化数据库连接"""
        if self.engine is not None:
            return

        database_url = settings.get_database_url()
        self.engine = create_async_engine(database_url, echo=False)
        self.sessionmaker = async_sessionmaker(
            self.engine, expire_on_commit=False, class_=AsyncSession
        )

        # 使用同步引擎创建表
        await asyncio.to_thread(self._sync_create_tables)

        logger.info(f"数据库管理器初始化完成: {database_url}")

    def _sync_create_tables(self):
        """使用同步引擎安全地创建所有表"""
        database_url = settings.get_database_url()

        # 根据数据库类型转换为同步URL
        if "sqlite+aiosqlite" in database_url:
            sync_db_url = database_url.replace("sqlite+aiosqlite", "sqlite")
        elif "mysql+aiomysql" in database_url:
            sync_db_url = database_url.replace("mysql+aiomysql", "mysql+pymysql")
        elif "postgresql+asyncpg" in database_url:
            sync_db_url = database_url.replace(
                "postgresql+asyncpg", "postgresql+psycopg2"
            )
        else:
            # 如果是其他类型或已经是同步URL，直接使用
            sync_db_url = database_url

        sync_engine = create_engine(sync_db_url)
        Base.metadata.create_all(bind=sync_engine)

    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        if self.sessionmaker is None:
            raise RuntimeError("数据库未初始化，请先调用 init()")
        return self.sessionmaker()

    async def close(self):
        """关闭数据库连接"""
        if self.engine is not None:
            await self.engine.dispose()
            self.engine = None
            self.sessionmaker = None
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()


async def get_db_session():
    """FastAPI依赖函数：获取数据库会话"""
    session = await db_manager.get_session()
    try:
        yield session
    finally:
        await session.close()


# 应用启动时初始化数据库
async def init_database():
    """初始化数据库连接"""
    await db_manager.init()


# 应用关闭时清理数据库连接
async def cleanup_database():
    """清理数据库连接"""
    await db_manager.close()
