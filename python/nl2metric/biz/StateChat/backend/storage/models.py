"""
数据库模型定义
"""

import json
from datetime import datetime
from typing import Any

from sqlalchemy import Column, String, Text, DateTime, Enum as SQLE<PERSON>, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.types import TypeDecorator, TEXT

from models.session import SessionStatus

Base = declarative_base()


class JSONEncodedDict(TypeDecorator):
    """将Python字典编码为JSON字符串存储在数据库中"""

    impl = TEXT
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value, ensure_ascii=False)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value


def serialize_json_field(data: Any) -> str:
    """将Python对象序列化为JSON字符串"""

    def default_serializer(o):
        if isinstance(o, datetime):
            return o.isoformat()
        raise TypeError(
            f"Object of type {o.__class__.__name__} is not JSON serializable"
        )

    return json.dumps(data, ensure_ascii=False, default=default_serializer)


def deserialize_json_field(data: str) -> Any:
    """将JSON字符串反序列化为Python对象"""
    return json.loads(data)


class SessionTable(Base):
    """用户会话表"""

    __tablename__ = "user_sessions"

    session_id = Column(String(255), primary_key=True, index=True)
    user_id = Column(String(255), index=True, nullable=True)
    flow_name = Column(String(255), nullable=False)
    current_state = Column(String(255), nullable=False)
    status = Column(
        SQLEnum(SessionStatus), default=SessionStatus.ACTIVE, nullable=False
    )
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.now, onupdate=datetime.now, nullable=False
    )


class ContextTable(Base):
    """流程上下文表"""

    __tablename__ = "flow_contexts"

    session_id = Column(String(255), primary_key=True, index=True)
    flow_name = Column(String(255), nullable=False)
    form_data = Column(JSONEncodedDict, default={})
    uploaded_files = Column(JSONEncodedDict, default={})
    business_info = Column(JSONEncodedDict, default={})
    personal_info = Column(JSONEncodedDict, default={})
    license_info = Column(JSONEncodedDict, default={})
    completed_steps = Column(JSONEncodedDict, default=[])
    pending_approvals = Column(JSONEncodedDict, default=[])
    generated_documents = Column(JSONEncodedDict, default=[])
    chat_history = Column(JSONEncodedDict, default=[])  # 添加 chat_history 字段
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.now, onupdate=datetime.now, nullable=False
    )


class TransitionTable(Base):
    """状态转移历史表"""

    __tablename__ = "state_transitions"

    id = Column(String(255), primary_key=True, index=True)
    session_id = Column(String(255), index=True, nullable=False)
    from_state = Column(String(255), nullable=False)
    to_state = Column(String(255), nullable=False)
    condition = Column(Text, nullable=True)
    user_input = Column(JSONEncodedDict, default={})
    created_at = Column(DateTime, default=datetime.now, nullable=False)


class FileUploadErrorTable(Base):
    """文件上传错误记录表"""

    __tablename__ = "file_upload_errors"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    session_id = Column(String(255), index=True, nullable=False)
    filename = Column(String(255), nullable=False)
    error_message = Column(Text, nullable=False)
    upload_time = Column(DateTime, default=datetime.now, nullable=False)


class FileRecordTable(Base):
    """文件记录表"""

    __tablename__ = "file_records"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    session_id = Column(String(255), index=True, nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    upload_time = Column(DateTime, default=datetime.now, nullable=False)
    status = Column(String(20), default="active", nullable=False)  # active, deleted
    file_metadata = Column(JSONEncodedDict, default={})
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.now, onupdate=datetime.now, nullable=False
    )
