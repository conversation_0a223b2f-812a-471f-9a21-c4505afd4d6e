"""
完整的状态聊天流程示例和测试
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any

from langgraph_chat_flow import StateChatManager
from enhanced_llm_processor import EnhancedLLMProcessor
from file_validator import FileValidator, FileValidationConfig, FileValidationRule


class EnhancedStateChatManager(StateChatManager):
    """增强的状态聊天管理器"""

    def __init__(self, config_path: str):
        super().__init__(config_path)
        self.llm_processor = EnhancedLLMProcessor()

    async def handle_message_with_enhancement(
        self, session_id: str, message: str, files: List[Dict] = None
    ) -> Dict[str, Any]:
        """处理消息，使用增强功能"""
        # 获取当前会话状态
        session = self.sessions.get(session_id, {})
        current_state = (
            session.get("current_state") or self.flow.config["initial_state"]
        )
        current_node = self.flow.nodes.get(current_state)

        if not current_node:
            return {"error": "Invalid state"}

        # 根据节点类型处理
        if current_node.input_type == "choice":
            # 使用意图分类
            options = current_node.config.get("options", [])
            intent_result = await self.llm_processor.classify_intent(message, options)

            if intent_result.confidence > 0.6:
                message = intent_result.intent  # 使用识别的意图

        elif current_node.input_type == "text" and "requires" in current_node.config:
            # 提取结构化数据
            required_fields = current_node.config["requires"]
            prompt_hint = current_node.config.get("llm_prompt_hint", "")

            extracted_data = await self.llm_processor.extract_structured_data(
                message, required_fields, prompt_hint
            )

            # 如果有缺失字段，生成追问
            if extracted_data.missing_fields:
                follow_up = await self.llm_processor.generate_follow_up_question(
                    extracted_data.missing_fields, message
                )

                # 保存已提取的数据
                if session_id not in self.sessions:
                    self.sessions[session_id] = {
                        "current_state": current_state,
                        "user_data": {},
                    }

                self.sessions[session_id]["user_data"].update(
                    extracted_data.dict(exclude={"missing_fields"})
                )

                return {
                    "response": follow_up,
                    "current_state": current_state,
                    "user_data": self.sessions[session_id]["user_data"],
                    "is_complete": False,
                    "missing_fields": extracted_data.missing_fields,
                }

        # 使用原始处理逻辑
        return await self.handle_message(session_id, message, files)

    async def validate_and_process_files(
        self, session_id: str, files: List[Dict], validation_config: Dict
    ) -> Dict[str, Any]:
        """验证和处理文件"""
        # 创建验证器
        config = FileValidationConfig(**validation_config)
        validator = FileValidator(config)

        # 验证文件
        validation_result = validator.validate_files(files)

        if validation_result["is_valid"]:
            # 保存文件信息
            if session_id not in self.sessions:
                self.sessions[session_id] = {
                    "current_state": None,
                    "user_data": {},
                    "files": [],
                }

            self.sessions[session_id]["files"].extend(
                validation_result["validated_files"]
            )

            # 自动转移到下一个状态
            current_state = self.sessions[session_id]["current_state"]
            current_node = self.flow.nodes.get(current_state)

            if current_node and current_node.config.get("next"):
                next_state = current_node.config["next"]
                self.sessions[session_id]["current_state"] = next_state

                return {
                    "response": "文件验证通过，正在处理...",
                    "current_state": next_state,
                    "is_complete": False,
                }
        else:
            # 返回验证错误
            error_msg = "文件验证失败:\n" + "\n".join(validation_result["errors"])
            if validation_result["missing_requirements"]:
                error_msg += "\n缺少必需文件:\n" + "\n".join(
                    req["message"] for req in validation_result["missing_requirements"]
                )

            return {
                "response": error_msg,
                "current_state": self.sessions[session_id]["current_state"],
                "is_complete": False,
                "validation_errors": validation_result,
            }


async def test_complete_flow():
    """测试完整的流程"""
    print("=== 测试智能客服完整流程 ===\n")

    # 初始化管理器
    manager = EnhancedStateChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # 模拟完整对话流程
    test_scenarios = [
        {
            "step": 1,
            "message": "你好",
            "expected_state": "check_registration",
            "description": "欢迎用户",
        },
        {
            "step": 2,
            "message": "我还没有注册",
            "expected_state": "register",
            "description": "用户选择未注册",
        },
        {
            "step": 3,
            "message": "我想以个人身份注册",
            "expected_state": "collect_personal_info",
            "description": "选择个人注册",
        },
        {
            "step": 4,
            "message": "我叫李四，我的邮箱是****************",
            "expected_state": "collect_personal_info",
            "description": "提供部分个人信息（缺失手机号）",
        },
        {
            "step": 5,
            "message": "我的手机号是13900139000",
            "expected_state": "main_process",
            "description": "补充完整信息",
        },
        {
            "step": 6,
            "message": "我想办理服务A",
            "expected_state": "service_a",
            "description": "选择服务A",
        },
    ]

    for scenario in test_scenarios:
        print(f"--- 步骤 {scenario['step']}: {scenario['description']} ---")
        print(f"用户输入: {scenario['message']}")

        result = await manager.handle_message_with_enhancement(
            session_id, scenario["message"]
        )

        print(f"AI回复: {result['response']}")
        print(f"当前状态: {result['current_state']}")
        print(f"收集的数据: {result.get('user_data', {})}")

        if result.get("missing_fields"):
            print(f"缺失字段: {result['missing_fields']}")

        print(f"预期状态: {scenario['expected_state']}")
        print(
            f"状态匹配: {'✓' if result['current_state'] == scenario['expected_state'] else '✗'}"
        )
        print("\n" + "=" * 50 + "\n")

        # 短暂延迟，模拟真实对话
        await asyncio.sleep(0.5)

    # 测试文件上传
    print("--- 测试文件上传 ---")
    files = [
        {
            "filename": "身份证照片.jpg",
            "content_type": "image/jpeg",
            "size": 1024 * 1024,  # 1MB
        },
        {
            "filename": "服务申请表.pdf",
            "content_type": "application/pdf",
            "size": 512 * 1024,  # 512KB
        },
    ]

    validation_config = {
        "validation_strategy": "basic",
        "allowed_types": ["image/jpeg", "image/png", "application/pdf"],
        "max_size_mb": 5,
        "required_files": [
            {
                "name_pattern": ".*身份证.*|.*ID.*",
                "description": "身份证照片",
                "min_count": 1,
            },
            {
                "name_pattern": ".*申请表.*|.*application.*",
                "description": "申请表格",
                "min_count": 1,
            },
        ],
        "auto_validate": True,
    }

    file_result = await manager.validate_and_process_files(
        session_id, files, validation_config
    )
    print(f"文件处理结果: {file_result['response']}")
    print(f"当前状态: {file_result['current_state']}")
    print("\n" + "=" * 50 + "\n")

    # 获取会话总结
    session_info = manager.get_session_info(session_id)
    print("=== 会话总结 ===")
    print(f"会话ID: {session_id}")
    print(f"当前状态: {session_info.get('current_state')}")
    print(f"收集的用户数据: {session_info.get('user_data', {})}")
    print(f"上传的文件数量: {len(session_info.get('files', []))}")


async def test_llm_enhancements():
    """测试LLM增强功能"""
    print("\n=== 测试LLM增强功能 ===\n")

    llm_processor = EnhancedLLMProcessor()

    # 测试数据提取
    print("--- 测试结构化数据提取 ---")
    test_text = "我叫王五，我的邮箱是***************，手机号码是15800158000"
    required_fields = ["name", "email", "phone"]

    extracted = await llm_processor.extract_structured_data(test_text, required_fields)
    print(f"输入文本: {test_text}")
    print(f"提取结果: {extracted}")
    print("\n")

    # 测试意图分类
    print("--- 测试意图分类 ---")
    test_intent = "我想办理企业注册，我们公司是做软件开发的"
    options = ["个人", "企业"]

    intent = await llm_processor.classify_intent(test_intent, options)
    print(f"输入文本: {test_intent}")
    print(f"分类结果: {intent}")
    print("\n")

    # 测试追问生成
    print("--- 测试追问生成 ---")
    missing_fields = ["phone", "email"]
    context = "用户正在注册个人信息，已提供姓名：张三"

    follow_up = await llm_processor.generate_follow_up_question(missing_fields, context)
    print(f"缺失字段: {missing_fields}")
    print(f"生成的追问: {follow_up}")
    print("\n")


async def main():
    """主函数"""
    await test_complete_flow()
    await test_llm_enhancements()


if __name__ == "__main__":
    asyncio.run(main())
