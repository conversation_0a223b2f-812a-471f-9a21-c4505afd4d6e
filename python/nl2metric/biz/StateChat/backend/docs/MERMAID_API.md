# 🎯 StateChat Mermaid流程图生成API

## 📋 概述

StateChat系统现在支持根据flow config动态生成Mermaid流程图，提供了强大的可视化功能来展示基于PyTransitions的状态机流程。

## 🚀 功能特性

### ✨ 核心功能
- **动态生成**: 根据YAML配置文件自动生成Mermaid图表
- **多种图表类型**: 支持流程图(flowchart)和状态图(stateDiagram)
- **样式主题**: 提供默认、彩色、简约三种主题
- **详细程度控制**: 可选择包含或隐藏状态详细信息
- **统计信息**: 提供流程统计数据

### 🎨 支持的图表类型
1. **Flowchart (流程图)** - 推荐
   - 清晰展示状态转换流程
   - 支持条件分支和线性流程
   - 丰富的样式和颜色主题

2. **State Diagram (状态图)**
   - 标准UML状态图格式
   - 适合技术文档
   - 简洁的状态转换表示

### 🎭 样式主题
- **default**: 默认蓝色主题，适合正式文档
- **colorful**: 彩色主题，不同类型状态使用不同颜色
- **minimal**: 简约主题，黑白风格

## 📡 API接口

### 1. POST /api/flows/mermaid
**动态生成Mermaid流程图 (推荐)**

```bash
curl -X POST "http://localhost:5432/api/flows/mermaid" \
  -H "Content-Type: application/json" \
  -d '{
    "flow_name": "simple_test_flow",
    "diagram_type": "flowchart", 
    "include_details": true,
    "style_theme": "colorful"
  }'
```

**请求参数:**
- `flow_name` (可选): 流程名称，不提供则使用默认流程
- `diagram_type`: 图表类型 (`flowchart` | `stateDiagram`)
- `include_details`: 是否包含状态详细信息 (boolean)
- `style_theme`: 样式主题 (`default` | `colorful` | `minimal`)

### 2. GET /api/flows/{flow_name}/mermaid
**获取指定流程的Mermaid图表 (便于浏览器访问)**

```bash
curl "http://localhost:5432/api/flows/simple_test_flow/mermaid?diagram_type=flowchart&include_details=true&style_theme=default"
```

**URL参数:**
- `flow_name`: 流程名称 (路径参数)
- `diagram_type`: 图表类型
- `include_details`: 是否包含详细信息 (`true` | `false`)
- `style_theme`: 样式主题

### 3. 响应格式

```json
{
  "flow_name": "simple_test_flow",
  "diagram_type": "flowchart",
  "mermaid_code": "graph TD\n    Start([开始]) --> welcome_user\n    ...",
  "statistics": {
    "total_states": 10,
    "decision_states": 3,
    "linear_states": 6,
    "final_states": 1,
    "initial_state": "welcome_user",
    "flow_name": "simple_test_flow",
    "version": "1.0.0",
    "description": "xxx公司服务xxx流程"
  },
  "generated_at": "2025-07-26T12:03:15.056059"
}
```

## 🌐 Web界面

访问 `http://localhost:5432/static/mermaid_demo.html` 使用交互式Web界面：

### 功能特性:
- 🎛️ **交互式控制**: 选择流程、图表类型、样式主题
- 📊 **实时预览**: 即时生成和显示Mermaid图表
- 📈 **统计信息**: 显示流程统计数据
- 🎨 **样式切换**: 动态切换不同主题
- ❌ **错误处理**: 友好的错误提示

## 📊 使用示例

### 示例1: 生成简单测试流程图
```bash
curl -X POST "http://localhost:5432/api/flows/mermaid" \
  -H "Content-Type: application/json" \
  -d '{
    "flow_name": "simple_test_flow",
    "diagram_type": "flowchart",
    "include_details": true,
    "style_theme": "colorful"
  }' | jq '.mermaid_code' -r > simple_flow.mmd
```

### 示例2: 生成咖啡店流程状态图
```bash
curl "http://localhost:5432/api/flows/cafe_license_application/mermaid?diagram_type=stateDiagram&include_details=false" \
  | jq '.mermaid_code' -r > cafe_states.mmd
```

### 示例3: 获取流程统计信息
```bash
curl -X POST "http://localhost:5432/api/flows/mermaid" \
  -H "Content-Type: application/json" \
  -d '{"flow_name": "cafe_license_application"}' \
  | jq '.statistics'
```

## 🛠️ 查看和编辑Mermaid图表

### 在线工具
1. **Mermaid Live Editor**: https://mermaid.live/
   - 复制生成的mermaid_code到编辑器
   - 实时预览和编辑
   - 导出为PNG/SVG

### 本地工具
1. **Mermaid CLI**:
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i input.mmd -o output.png
   ```

2. **VS Code插件**:
   - 安装 "Mermaid Preview" 插件
   - 直接在VS Code中预览.mmd文件

## 🔧 技术实现

### 核心组件
- **MermaidFlowGenerator**: 核心生成器类
- **FlowConfig**: 流程配置模型
- **PyTransitions**: 状态机引擎

### 生成流程
1. 加载FlowConfig配置
2. 解析状态和转换规则
3. 生成Mermaid语法
4. 应用样式主题
5. 返回完整图表代码

### 状态类型识别
- **决策状态**: 包含多个条件分支的状态
- **线性状态**: 单一转换路径的状态
- **终止状态**: 流程结束状态
- **收集状态**: 信息收集相关状态

## 🎯 最佳实践

### 1. 选择合适的图表类型
- **流程图**: 适合业务流程展示，更直观
- **状态图**: 适合技术文档，更标准

### 2. 样式主题选择
- **彩色主题**: 适合演示和培训
- **默认主题**: 适合正式文档
- **简约主题**: 适合打印和黑白显示

### 3. 详细信息控制
- **包含详情**: 适合详细分析和调试
- **隐藏详情**: 适合概览和高层次展示

### 4. 性能优化
- 复杂流程建议使用简约主题
- 大型流程可考虑分段生成
- 缓存生成结果以提高响应速度

## 🚨 注意事项

1. **流程名称**: 确保流程名称在系统中存在
2. **图表大小**: 复杂流程可能生成较大的图表
3. **浏览器兼容**: 建议使用现代浏览器查看
4. **中文支持**: Mermaid完全支持中文标签

## 📚 相关文档

- [PyTransitions官方文档](https://github.com/pytransitions/transitions)
- [Mermaid官方文档](https://mermaid.js.org/)
- [StateChat配置指南](./CONFIG_GUIDE.md)
- [API完整文档](http://localhost:5432/docs)
