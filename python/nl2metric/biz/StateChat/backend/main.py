"""
状态机引擎主应用入口
"""

import os
import subprocess
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn

# .env
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from utils.logger import setup_logger
# 导入配置
from utils.app_config import settings

load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI应用生命周期事件"""
    from storage.database import init_database, cleanup_database

    # 应用启动时执行
    await init_database()
    if os.environ.get("trace_langchain") == "true":
        trace_langchain(app)

    # 设置日志
    log_dir = Path(settings.logging.log_dir).parent
    log_dir.mkdir(exist_ok=True)
    print(settings.logging.level, settings.logging.log_dir)
    setup_logger(log_level=settings.logging.level, log_dir=settings.logging.log_dir)

    print("🚀 启动状态机引擎服务...")
    print("📋 咖啡店营业执照申请流程")
    print(f"🌐 API文档: http://{settings.app.host}:{settings.app.port}/docs")
    print(f"📊 健康检查: http://{settings.app.host}:{settings.app.port}/health")
    print(f"🗄️  数据库: {settings.get_database_url()}")
    print(f"📁 上传目录: {settings.app.uploads_dir}")
    print(f"🔄 默认流程: {settings.app.default_flow_name}")


    yield
    # 应用关闭时执行
    await cleanup_database()
    # 终止 phoenix 进程
    if hasattr(app.state, "phoenix_process") and app.state.phoenix_process:
        app.state.phoenix_process.terminate()
        try:
            app.state.phoenix_process.wait(timeout=5)  # 等待进程终止，超时5秒
            print("Phoenix server terminated.")
        except subprocess.TimeoutExpired:
            app.state.phoenix_process.kill()
            print("Phoenix server did not terminate gracefully, killed it.")


app = FastAPI(
    title="状态机引擎API",
    description="基于配置驱动的状态机引擎 - 咖啡店营业执照申请流程",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

from middleware.logging_middleware import RequestResponseLoggingMiddleware

app.add_middleware(RequestResponseLoggingMiddleware)

from api.endpoints import router

app.include_router(router)


def trace_langchain(app: FastAPI):
    cmd = ["python", "-m", "phoenix.server.main", "serve"]
    # 后台启动 phoenix，将输出重定向到 DEVNULL，不显示在控制台
    app.state.phoenix_process = subprocess.Popen(
        cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL
    )

    from openinference.instrumentation.langchain import LangChainInstrumentor
    from opentelemetry import trace as trace_api
    from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
    from opentelemetry.sdk import trace as trace_sdk
    from opentelemetry.sdk.trace.export import ConsoleSpanExporter, SimpleSpanProcessor

    # 可以通过环境变量配置追踪端点
    endpoint = os.getenv("PHOENIX_ENDPOINT", "http://localhost:6006/v1/traces")
    tracer_provider = trace_sdk.TracerProvider()
    trace_api.set_tracer_provider(tracer_provider)
    tracer_provider.add_span_processor(SimpleSpanProcessor(OTLPSpanExporter(endpoint)))
    # tracer_provider.add_span_processor(SimpleSpanProcessor(ConsoleSpanExporter()))

    LangChainInstrumentor().instrument()
    print("LangChain instrumentation enabled")
    print("endpoint:", endpoint)


def main():
    """主函数"""
    # 启动FastAPI应用
    uvicorn.run(
        app,
        host=settings.app.host,
        port=settings.app.port,
        # reload=True,  # 开发模式下启用热重载
        log_level=settings.logging.level.lower(),
    )


if __name__ == "__main__":
    main()
