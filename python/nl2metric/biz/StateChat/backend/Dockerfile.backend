FROM registry.gitlab.dipeak.com/dipeak/generic-repository/python:keep-3.11.13-slim-bookworm AS base

RUN pip config set global.index-url https://mirrors.bfsu.edu.cn/pypi/web/simple
RUN sed -i "<EMAIL>@mirrors.ustc.edu.cn@g" /etc/apt/sources.list.d/debian.sources

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# Builder stage with build dependencies
FROM base AS builder

WORKDIR /app

# 安装系统依赖和uv
RUN apt-get update \
    && apt-get install -y build-essential libpq-dev gcc curl \
    && pip install uv==0.7.13 \
    && rm -rf /var/lib/apt/lists/*


ENV UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple

# 复制项目代码
COPY backend/pyproject.toml .
COPY backend/uv.lock .
COPY backend/README.md .
COPY backend/api/ api/
COPY backend/config/ config/
COPY backend/core/ core/
COPY backend/models/ models/
COPY backend/prompts/ prompts/
COPY backend/middleware/ middleware/
COPY backend/scripts/ scripts/
COPY backend/services/ services/
COPY backend/storage/ storage/
COPY backend/utils/ utils/
COPY backend/main.py .

# 使用uv安装依赖
RUN uv sync --frozen

EXPOSE 5432

CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5432"]