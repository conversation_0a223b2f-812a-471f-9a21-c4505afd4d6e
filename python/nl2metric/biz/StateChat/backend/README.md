# StateChat - 智能状态机引擎 🚀

<div align="center">

![Python](https://img.shields.io/badge/Python-3.13+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green.svg)
![React](https://img.shields.io/badge/React-18+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)

**一个现代化的、配置驱动的状态机引擎，专为复杂业务流程设计**

[🚀 快速开始](#-快速开始) • [📖 文档](#-文档) • [🎯 特性](#-核心特性) • [🌐 演示](#-在线演示) • [🤝 贡献](#-贡献)

</div>

---

## 🎯 核心特性

### 🔧 **配置驱动架构**
- 📝 **YAML配置**: 通过配置文件定义复杂业务流程，零代码修改
- 🔄 **动态加载**: 支持热重载配置，实时更新流程定义
- 🎛️ **多环境支持**: 开发、测试、生产环境独立配置

### 🧠 **智能状态管理**
- 🎯 **PyTransitions引擎**: 基于成熟的状态机库，支持复杂转移逻辑
- 📊 **进度计算**: 智能BFS算法计算流程进度，精确到状态级别
- 💾 **会话持久化**: 支持会话暂停/恢复，数据永不丢失
- 🔍 **意图识别**: AI驱动的用户意图理解，支持闲聊/查询/业务办理

### 🎨 **可视化与前端**
- 📈 **Mermaid图表**: 动态生成流程图和状态图，支持多种主题
- ⚛️ **React前端**: 现代化Web界面，实时交互和可视化
- 🎭 **多主题支持**: 默认、彩色、简约三种视觉主题
- 📱 **响应式设计**: 完美适配桌面和移动设备

### 🌐 **企业级API**
- ⚡ **FastAPI框架**: 高性能异步API，自动生成文档
- 🔒 **类型安全**: Pydantic数据验证，确保数据完整性
- 📊 **实时监控**: 详细的日志记录和性能监控
- 🔄 **RESTful设计**: 标准化API接口，易于集成

## 🏗️ 系统架构

<div align="center">

```mermaid
graph TB
    subgraph "前端层 Frontend"
        A[React Web App] --> B[Mermaid可视化]
        A --> C[实时交互界面]
    end

    subgraph "API层 API Layer"
        D[FastAPI服务] --> E[状态机API]
        D --> F[Mermaid生成API]
        D --> G[会话管理API]
    end

    subgraph "核心引擎 Core Engine"
        H[PyTransitions状态机] --> I[配置驱动引擎]
        H --> J[智能进度计算]
        H --> K[意图识别]
    end

    subgraph "数据层 Data Layer"
        L[SQLite/MySQL/PostgreSQL] --> M[会话存储]
        L --> N[上下文管理]
        L --> O[文件存储]
    end

    A --> D
    D --> H
    H --> L
```

</div>

### 📁 项目结构

```
StateChat/
├── 🎯 core/                   # 核心状态机引擎
│   ├── state_machine.py       # 状态机主引擎 + BFS进度计算
│   ├── flow_manager.py        # 流程管理器
│   ├── intent.py              # AI意图识别
│   └── exceptions.py          # 异常处理
├── 📊 models/                 # 数据模型层
│   ├── session.py             # 用户会话模型
│   ├── context.py             # 流程上下文
│   └── state.py               # 状态节点定义
├── 🌐 api/                    # RESTful API层
│   ├── endpoints.py           # FastAPI路由定义
│   └── schemas.py             # Pydantic数据模型
├── 💾 storage/                # 数据持久化层
│   ├── database.py            # 多数据库支持
│   ├── session_store.py       # 会话存储管理
│   └── models.py              # SQLAlchemy模型
├── 🛠️ utils/                  # 工具库
│   ├── app_config.py          # 统一配置管理
│   ├── config_loader.py       # YAML配置加载
│   ├── mermaid_generator.py   # 流程图生成器
│   └── logger.py              # 日志系统
├── ⚛️ frontend/               # React前端应用
│   ├── src/components/        # React组件
│   ├── src/pages/             # 页面组件
│   └── public/                # 静态资源
├── ⚙️ config/                 # 配置文件
│   ├── config.yaml            # 应用主配置
│   └── flow_config/           # 流程定义文件
│       ├── simple_test_flow.yaml
│       └── cafe_license_flow.yaml
└── 🧪 test/                   # 测试套件
    ├── test_config.py         # 配置系统测试
    ├── smoke_test.py          # 冒烟测试
    └── test_progress_*.py     # 进度计算测试
```

## 🚀 快速开始

### 📋 环境要求

- **Python**: 3.13+ (推荐使用最新版本)
- **Node.js**: 18+ (用于前端开发)
- **包管理器**: uv (推荐) 或 pip + npm

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd StateChat

# 2. 安装Python依赖 (推荐使用uv)
uv sync
# 或使用传统方式: pip install -r requirements.txt

# 3. 启动后端服务
python main.py

# 4. 启动前端服务 (新终端)
cd frontend
npm install
npm start
```

### 🌐 访问应用

启动成功后，您可以访问：

| 服务 | URL | 描述 |
|------|-----|------|
| 🎯 **主应用** | http://localhost:3000 | React前端界面 |
| 📚 **API文档** | http://localhost:5432/docs | 自动生成的API文档 |
| 💓 **健康检查** | http://localhost:5432/health | 服务状态监控 |
| 📊 **Mermaid可视化** | http://localhost:3000/mermaid | 流程图可视化界面 |

### 🧪 运行测试

```bash
# 配置系统测试
python test/test_config.py

# 集成测试
python test/test_config_integration.py

# 冒烟测试 (端到端)
python test/smoke_test.py

# 进度计算测试
python test/test_progress_calculation.py
```

## 🎨 可视化与前端

### 📊 Mermaid流程图生成

StateChat提供强大的流程可视化功能，支持动态生成Mermaid图表：

#### 🎯 核心功能
- **动态生成**: 根据YAML配置自动生成流程图
- **多种类型**: 支持流程图(flowchart)和状态图(stateDiagram)
- **主题切换**: 默认、彩色、简约三种视觉主题
- **实时预览**: Web界面实时生成和预览

#### 🌐 Web界面使用

访问 http://localhost:3000/mermaid 使用交互式可视化界面：

```bash
# 生成流程图API
curl -X POST "http://localhost:5432/api/flows/mermaid" \
  -H "Content-Type: application/json" \
  -d '{
    "flow_name": "simple_test_flow",
    "diagram_type": "flowchart",
    "include_details": true,
    "style_theme": "colorful"
  }'
```

### ⚛️ React前端应用

现代化的Web界面，提供完整的用户体验：

#### 🎛️ 主要功能
- **流程交互**: 实时状态机交互和流程执行
- **可视化面板**: Mermaid图表集成和主题切换
- **会话管理**: 完整的会话生命周期管理
- **文件上传**: 拖拽式文件上传界面
- **进度跟踪**: 实时进度显示和状态更新

#### 🚀 前端开发

```bash
cd frontend

# 安装依赖
npm install

# 开发模式
npm start

# 构建生产版本
npm run build
```

## 🧠 智能意图识别

### 🎯 意图类型

| 意图类型 | 描述 | 示例 | 处理方式 |
|---------|------|------|---------|
| 💬 **CHAT** | 日常对话、问候 | "你好"、"天气真好" | 友好回复，不影响流程 |
| 🔍 **RAG_QUERY** | 知识查询、咨询 | "如何注册公司？" | 知识库查询，不影响流程 |
| 📋 **BUSINESS** | 业务办理操作 | "申请营业执照" | 继续执行业务流程 |
| ❓ **UNKNOWN** | 未知意图 | 模糊输入 | 默认作为业务输入处理 |

### 💻 代码示例

```python
from core.intent import IntentProcessor

# 初始化意图处理器
processor = IntentProcessor()

# 智能意图识别
intent = processor.process("你好，我想申请营业执照")
print(f"识别意图: {intent.value}")  # business

# 处理用户输入
result = processor.handle_input(
    "如何注册公司？",
    {"session_id": "123", "current_state": "welcome"}
)

# 自定义意图规则
processor.intent_patterns[IntentType.CHAT].extend([
    "打招呼", "问候", "闲聊"
])
```

## 📁 文件验证系统

### 🎯 核心功能

StateChat提供了完整的文件验证功能，支持复杂业务流程中的文件上传和验证需求：

#### 📋 支持的验证类型
- **文件类型验证**: 检查MIME类型是否符合要求
- **文件大小验证**: 限制最大文件大小
- **文件名验证**: 智能模式匹配命名规范
- **必需文件验证**: 正则表达式匹配必需文件
- **文件完整性验证**: 检查文件存在性和可读性
- **多模态验证**: 预留AI内容验证接口

#### 🔧 配置化验证规则

```yaml
# 在状态配置中定义文件验证规则
service_a:
  name: "service_a"
  prompt: "请上传您的身份证照片和申请表格。"
  input_type: "file"
  file_validation:
    allowed_types: ["image/jpeg", "image/png", "application/pdf"]
    max_size_mb: 5
    required_files:
      - name_pattern: ".*身份证.*|.*ID.*"
        description: "身份证照片"
        min_count: 1
      - name_pattern: ".*申请表.*|.*application.*"
        description: "申请表格"
        min_count: 1
    naming_convention:
      pattern: "{session_id}_{document_type}_{timestamp}"
      description: "文件名应包含会话ID、文档类型和时间戳"
    enable_multimodal: false
```

#### 🌐 文件验证API

```bash
# 验证已上传文件
curl -X POST "http://localhost:5432/sessions/{session_id}/files/validate" \
  -H "Authorization: Bearer {token}"

# 获取文件上传建议
curl -X GET "http://localhost:5432/sessions/{session_id}/files/suggestions" \
  -H "Authorization: Bearer {token}"

# 重命名文件
curl -X PUT "http://localhost:5432/sessions/{session_id}/files/rename" \
  -H "Authorization: Bearer {token}" \
  -d '{"current_filename": "old.jpg", "new_filename": "new.jpg"}'

# 多模态验证（预留接口）
curl -X POST "http://localhost:5432/sessions/{session_id}/files/multimodal-validate" \
  -H "Authorization: Bearer {token}" \
  -d '{"filename": "test.jpg", "expected_content": "身份证照片"}'
```

### 💻 代码示例

```python
from core.file_validator import default_file_validator
from models.file_validation import FileValidationConfig

# 创建验证配置
config = FileValidationConfig(
    allowed_types=["image/jpeg", "image/png"],
    max_size_mb=5,
    required_files=[
        FileValidationRule(
            name_pattern=".*身份证.*",
            description="身份证照片",
            min_count=1
        )
    ]
)

# 执行文件验证
result = await default_file_validator.validate_files(
    session_id="session_123",
    state=current_state,
    context=context
)

# 获取验证建议
suggestions = await default_file_validator.get_file_validation_suggestions(result)
```

## 📋 业务流程案例

### 🏪 新加坡咖啡店营业执照申请流程

本项目实现了完整的新加坡咖啡店营业执照申请流程，展示了复杂业务流程的配置化实现：

<details>
<summary>📊 <strong>查看完整流程图</strong></summary>

```mermaid
stateDiagram-v2
    [*] --> welcome_user
    welcome_user --> check_acra_registration

    state "ACRA注册流程" as acra {
        check_acra_registration --> register_acra: 否
        register_acra --> collect_business_info: 选择企业类型
        collect_business_info --> verify_identity
        verify_identity --> confirm_acra_info
        confirm_acra_info --> acra_payment: 确认
        acra_payment --> check_commercial_unit
    }

    check_acra_registration --> check_commercial_unit: 是

    state "商业单位流程" as commercial {
        check_commercial_unit --> find_commercial_unit: 否
        check_commercial_unit --> provide_commercial_unit_info: 是
        find_commercial_unit --> check_business_services
        provide_commercial_unit_info --> check_business_services
    }

    check_business_services --> done
    done --> [*]
```

</details>

#### 🎯 主要流程模块

| 模块 | 功能 | 状态数量 | 复杂度 |
|------|------|----------|--------|
| 🏢 **ACRA注册** | 企业结构选择、信息收集、身份验证 | 8个状态 | 高 |
| 🏪 **商业单位** | 地产租赁、地址布局图 | 4个状态 | 中 |
| 📜 **许可证申请** | 酒类、宠物友好、户外座位许可 | 6个状态 | 中 |
| 🔥 **安全流程** | 消防安全证书、防火计划 | 3个状态 | 低 |
| 🪧 **招牌许可** | 招牌设计审批 | 2个状态 | 低 |
| 👥 **雇佣流程** | 公积金雇主注册 | 2个状态 | 低 |
| 🍽️ **食品许可** | 食品店许可证申请 | 3个状态 | 中 |
| 💰 **财务设置** | 银行账户、GST注册、簿记 | 4个状态 | 中 |

### 🧪 简单测试流程

为了便于开发和测试，项目还包含一个简化的测试流程：

```yaml
# config/flow_config/simple_test_flow.yaml
flow_name: "simple_test_flow"
description: "简单测试流程 - 演示基本功能"
version: "1.0.0"

states:
  welcome_user:
    prompt: "您好！欢迎使用xxx公司服务系统"
    input_type: "text"
    next: "check_registration"

  check_registration:
    prompt: "您是否已经注册？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "main_process"
      否: "register_first"
```

## ⚙️ 配置系统

### 📝 统一配置管理

StateChat采用现代化的配置管理系统，支持多环境和类型安全：

```yaml
# config/config.yaml - 主配置文件
app:
  uploads_dir: "uploads"
  default_flow_name: "simple_test_flow"
  host: "0.0.0.0"
  port: 5432
  environment: "development"  # development | test | production

# 多环境数据库配置
database:
  development:
    type: sqlite
    database: data/state_chat.db
    echo: true
  production:
    type: mysql
    host: ${DB_HOST}
    port: ${DB_PORT:-3306}
    database: ${DB_NAME}
    user: ${DB_USER}
    password: ${DB_PASSWORD}

api:
  base_url: "http://localhost:5432"
  timeout: 30
  max_retries: 3
```

### 🔄 流程配置格式

流程定义采用直观的YAML格式：

```yaml
# config/flow_config/example_flow.yaml
flow_name: "example_flow"
description: "示例业务流程"
version: "1.0.0"
initial_state: "welcome_user"
final_states: ["done"]

states:
  welcome_user:
    prompt: "您好！我是AI助手，很高兴为您服务。"
    input_type: "text"
    next: "check_registration"

  check_registration:
    prompt: "您是否已经注册？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "main_process"
      否: "register_first"

  # 支持复杂的条件分支
  main_process:
    prompt: "请选择您需要的服务："
    input_type: "choice"
    options: ["服务A", "服务B", "服务C"]
    next:
      服务A: "process_service_a"
      服务B: "process_service_b"
      服务C: "process_service_c"
```

### 🎛️ 配置特性

- **🔒 类型安全**: Pydantic模型验证，确保配置正确性
- **🌍 环境变量**: 支持 `${VAR_NAME}` 语法和默认值
- **🔄 热重载**: 开发环境支持配置文件热重载
- **📊 多数据库**: SQLite、MySQL、PostgreSQL支持
- **🎯 路径解析**: 自动处理相对路径和绝对路径

## 🌐 API接口文档

### 📋 核心API端点

| 方法 | 端点 | 描述 | 状态 |
|------|------|------|------|
| `POST` | `/sessions` | 创建新会话 | ✅ |
| `GET` | `/sessions/{session_id}` | 获取会话详情 | ✅ |
| `POST` | `/sessions/{session_id}/input` | 处理用户输入 | ✅ |
| `GET` | `/sessions/{session_id}/state` | 获取当前状态 | ✅ |
| `POST` | `/sessions/{session_id}/files` | 文件上传 | ✅ |
| `PUT` | `/sessions/{session_id}/pause` | 暂停会话 | ✅ |
| `PUT` | `/sessions/{session_id}/resume` | 恢复会话 | ✅ |

### 📊 Mermaid可视化API

| 方法 | 端点 | 描述 | 特性 |
|------|------|------|------|
| `POST` | `/api/flows/mermaid` | 动态生成流程图 | 🎨 多主题 |
| `GET` | `/api/flows/{flow_name}/mermaid` | 获取指定流程图 | 📊 统计信息 |
| `GET` | `/api/flows` | 获取可用流程列表 | 📋 流程管理 |

### 💻 API使用示例

#### 创建会话并开始流程

```bash
# 1. 创建新会话
curl -X POST "http://localhost:5432/sessions" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "flow_name": "simple_test_flow"}'

# 响应示例
{
  "session_id": "sess_abc123",
  "user_id": "user123",
  "flow_name": "simple_test_flow",
  "current_state": "welcome_user",
  "status": "active",
  "progress": 0.0
}
```

#### 处理用户输入

```bash
# 2. 发送用户输入
curl -X POST "http://localhost:5432/sessions/sess_abc123/input" \
  -H "Content-Type: application/json" \
  -d '{
    "input_type": "text",
    "value": "我要申请营业执照"
  }'

# 响应示例
{
  "success": true,
  "current_state": "check_registration",
  "prompt": "您是否已经注册？",
  "input_type": "choice",
  "options": ["是", "否"],
  "progress": 25.0,
  "session_summary": "用户开始申请流程"
}
```

#### 生成流程可视化

```bash
# 3. 生成Mermaid流程图
curl -X POST "http://localhost:5432/api/flows/mermaid" \
  -H "Content-Type: application/json" \
  -d '{
    "flow_name": "simple_test_flow",
    "diagram_type": "flowchart",
    "style_theme": "colorful",
    "include_details": true
  }'
```

## 🧪 测试与质量保证

### 🔬 测试套件

StateChat包含完整的测试套件，确保系统稳定性和可靠性：

```bash
# 🔧 配置系统测试
python test/test_config.py                    # 配置加载和验证
python test/test_config_integration.py       # 配置集成测试

# 🚀 核心功能测试
python test/smoke_test.py                     # 端到端冒烟测试
python test/test_progress_calculation.py     # 进度计算算法测试
python test/test_progress_integration.py     # 进度集成测试

# 📊 Mermaid可视化测试
python test/test_mermaid_api.py              # 图表生成API测试
```

### 📈 测试覆盖率

| 模块 | 覆盖率 | 状态 | 描述 |
|------|--------|------|------|
| 🔧 **配置系统** | 95%+ | ✅ | 多环境配置、类型验证 |
| 🧠 **状态机引擎** | 90%+ | ✅ | 状态转移、会话管理 |
| 📊 **进度计算** | 100% | ✅ | BFS算法、路径分析 |
| 🎨 **Mermaid生成** | 85%+ | ✅ | 图表生成、主题切换 |
| 🌐 **API接口** | 80%+ | ✅ | 端点测试、错误处理 |

### 🎯 性能基准

```bash
# 性能测试结果 (在标准开发机器上)
✅ 会话创建: < 50ms
✅ 状态转移: < 30ms
✅ 进度计算: < 10ms
✅ Mermaid生成: < 200ms
✅ 数据库查询: < 20ms
```

## 🌟 在线演示

### 🎮 交互式演示

访问我们的在线演示，体验完整功能：

| 演示类型 | URL | 描述 |
|----------|-----|------|
| 🎯 **完整应用** | http://localhost:3000 | React前端 + 完整功能 |
| 📊 **流程可视化** | http://localhost:3000/mermaid | Mermaid图表生成器 |
| 📚 **API文档** | http://localhost:5432/docs | 交互式API文档 |

### 🚀 快速体验

```bash
# 一键启动演示
git clone <repository-url>
cd StateChat
uv sync && python main.py

# 在另一个终端启动前端
cd frontend && npm install && npm start

# 访问 http://localhost:3000 开始体验！
```

## 🛠️ 开发与扩展

### 🚀 添加新业务流程

1. **创建流程配置**
```bash
# 在 config/flow_config/ 目录下创建新的YAML文件
cp config/flow_config/simple_test_flow.yaml config/flow_config/my_new_flow.yaml
```

2. **定义流程结构**
```yaml
flow_name: "my_new_flow"
description: "我的新业务流程"
version: "1.0.0"
initial_state: "start"
final_states: ["completed"]

states:
  start:
    prompt: "欢迎使用新流程"
    input_type: "text"
    next: "next_step"
```

3. **测试新流程**
```bash
# 使用API测试新流程
curl -X POST "http://localhost:5432/sessions" \
  -d '{"user_id": "test", "flow_name": "my_new_flow"}'
```

### 🔧 自定义扩展

#### 自定义验证规则
```python
from core.transition import default_transition_engine

def email_validation(state, user_input, context):
    """自定义邮箱验证规则"""
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, user_input) is not None

# 注册自定义验证规则
default_transition_engine.register_validation_rule("email", email_validation)
```

#### 自定义状态转移逻辑
```python
def conditional_transition(current_state, user_input, context):
    """基于上下文的条件转移"""
    if context.get("user_type") == "premium":
        return "premium_process"
    else:
        return "standard_process"

# 注册自定义转移规则
default_transition_engine.register_transition_rule("conditional_state", conditional_transition)
```

### 📊 监控与日志

#### 日志系统
```python
# 使用loguru进行结构化日志记录
from loguru import logger

logger.info("用户 {user_id} 开始流程 {flow_name}",
           user_id=session.user_id,
           flow_name=session.flow_name)
```

#### 日志配置
- **控制台输出**: 彩色格式化，开发友好
- **文件输出**: 结构化JSON格式，便于分析
- **日志轮转**: 自动压缩和清理旧日志
- **日志级别**: DEBUG, INFO, WARNING, ERROR, CRITICAL

#### 性能监控
```bash
# 查看系统性能指标
tail -f logs/state_chat.log | grep "performance"

# 监控API响应时间
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:5432/health
```

## 📚 文档

### 📖 完整文档

| 文档类型 | 位置 | 描述 |
|----------|------|------|
| 🎯 **API文档** | `/docs/MERMAID_API.md` | Mermaid可视化API详细说明 |
| ⚛️ **前端集成** | `/frontend/MERMAID_INTEGRATION.md` | React前端集成指南 |
| 📋 **项目总结** | `/PROJECT_SUMMARY.md` | 项目架构和技术总结 |
| 🔧 **配置指南** | 本README配置部分 | 配置系统使用说明 |

### 🌐 在线资源

- **FastAPI文档**: http://localhost:5432/docs
- **Mermaid官方**: https://mermaid.js.org/
- **PyTransitions**: https://github.com/pytransitions/transitions
- **React文档**: https://reactjs.org/

## 🔧 技术栈

### 🐍 后端技术

| 技术 | 版本 | 用途 | 特性 |
|------|------|------|------|
| **Python** | 3.13+ | 核心语言 | 现代语法、类型提示 |
| **FastAPI** | Latest | Web框架 | 高性能、自动文档 |
| **PyTransitions** | Latest | 状态机引擎 | 成熟、可靠 |
| **SQLAlchemy** | 2.0+ | ORM框架 | 异步支持、类型安全 |
| **Pydantic** | 2.0+ | 数据验证 | 类型安全、性能优化 |
| **Loguru** | Latest | 日志系统 | 结构化日志、易用 |

### ⚛️ 前端技术

| 技术 | 版本 | 用途 | 特性 |
|------|------|------|------|
| **React** | 18+ | UI框架 | 组件化、现代化 |
| **TypeScript** | 5+ | 类型系统 | 类型安全、开发体验 |
| **Ant Design** | 5+ | UI组件库 | 企业级、美观 |
| **Mermaid** | Latest | 图表渲染 | 动态图表、主题支持 |
| **React Router** | 6+ | 路由管理 | SPA路由 |

### 💾 数据存储

| 数据库 | 支持状态 | 用途 | 特性 |
|--------|----------|------|------|
| **SQLite** | ✅ 生产就绪 | 开发/小型部署 | 零配置、文件数据库 |
| **MySQL** | ✅ 生产就绪 | 中大型部署 | 高性能、集群支持 |
| **PostgreSQL** | ✅ 生产就绪 | 企业级部署 | 功能丰富、扩展性强 |

## 🚀 部署指南

### 🐳 Docker部署

```bash
# 构建镜像
docker build -t statechat:latest .

# 运行容器
docker run -d \
  --name statechat \
  -p 5432:5432 \
  -p 3000:3000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  statechat:latest
```

### ☁️ 云平台部署

<details>
<summary><strong>AWS部署</strong></summary>

```bash
# 使用AWS ECS
aws ecs create-cluster --cluster-name statechat-cluster
aws ecs register-task-definition --cli-input-json file://task-definition.json
aws ecs create-service --cluster statechat-cluster --service-name statechat-service
```

</details>

<details>
<summary><strong>Kubernetes部署</strong></summary>

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: statechat
spec:
  replicas: 3
  selector:
    matchLabels:
      app: statechat
  template:
    metadata:
      labels:
        app: statechat
    spec:
      containers:
      - name: statechat
        image: statechat:latest
        ports:
        - containerPort: 5432
        - containerPort: 3000
```

</details>

### 🔧 生产环境配置

```yaml
# config/config.yaml - 生产环境
app:
  environment: "production"
  host: "0.0.0.0"
  port: 5432
  log_level: "INFO"

database:
  production:
    type: mysql
    host: ${DB_HOST}
    port: ${DB_PORT:-3306}
    database: ${DB_NAME}
    user: ${DB_USER}
    password: ${DB_PASSWORD}
    pool_size: 20
    max_overflow: 10
```

## 🤝 贡献

### 🎯 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. **Fork项目** 🍴
```bash
git clone https://github.com/your-username/StateChat.git
cd StateChat
```

2. **创建功能分支** 🌿
```bash
git checkout -b feature/amazing-feature
```

3. **提交更改** 📝
```bash
git commit -m "Add: 添加了令人惊叹的功能"
```

4. **推送分支** 🚀
```bash
git push origin feature/amazing-feature
```

5. **创建Pull Request** 🔄

### 📋 贡献类型

- 🐛 **Bug修复**: 发现并修复问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档**: 改进文档和示例
- 🎨 **UI/UX**: 改进用户界面和体验
- ⚡ **性能**: 性能优化和改进
- 🧪 **测试**: 添加或改进测试用例

### 🔍 代码规范

```bash
# 代码格式化
black .
isort .

# 类型检查
mypy .

# 测试
pytest test/
```

## 📄 许可证

本项目采用 **MIT许可证** - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目和社区：

- [PyTransitions](https://github.com/pytransitions/transitions) - 强大的状态机库
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Web框架
- [Mermaid](https://mermaid.js.org/) - 优秀的图表生成工具
- [React](https://reactjs.org/) - 流行的前端框架
- [Ant Design](https://ant.design/) - 企业级UI组件库

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**

**🔗 [项目主页](https://github.com/your-username/StateChat) • [问题反馈](https://github.com/your-username/StateChat/issues) • [功能请求](https://github.com/your-username/StateChat/issues/new)**

**📧 联系我们: [<EMAIL>](mailto:<EMAIL>)**

</div>
