"""
文件管理服务模块
处理文件相关的数据库操作
"""

import os
from datetime import datetime
from typing import List, Optional

from fastapi import UploadFile
from loguru import logger
from sqlalchemy import select
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

from api.schemas import FileRecordResponse, FileListResponse
from core.file_validator import default_file_validator
from core.flow_manager import flow_manager
from core.state_machine import StateMachine
from models.context import FlowContext, FileUpload, MessageRole
from models.state import StateNode
from storage.models import FileRecordTable, FileUploadErrorTable
from utils.app_config import settings
import re


class FileService:
    """文件管理服务类"""

    @staticmethod
    async def create_file_record(
        db_session: AsyncSession,
        session_id: str,
        filename: str,
        original_filename: str,
        file_path: str,
        file_size: int,
        content_type: str,
        upload_time: datetime,
        metadata: dict = None,
    ) -> FileRecordTable:
        """创建文件记录"""
        try:
            file_record = FileRecordTable(
                session_id=session_id,
                filename=filename,
                original_filename=original_filename,
                file_path=file_path,
                file_size=file_size,
                content_type=content_type,
                upload_time=upload_time,
                status="active",
                file_metadata=metadata or {},
            )
            db_session.add(file_record)
            await db_session.commit()
            await db_session.refresh(file_record)
            return file_record
        except Exception as e:
            logger.exception(f"创建文件记录失败: {e}")
            await db_session.rollback()
            raise

    @staticmethod
    async def get_file_records(
        db_session: AsyncSession, session_id: str, status: str = "active"
    ) -> List[FileRecordTable]:
        """获取文件记录列表"""
        try:
            if status == "all":
                query = select(FileRecordTable).where(
                    FileRecordTable.session_id == session_id
                )
            else:
                query = select(FileRecordTable).where(
                    FileRecordTable.session_id == session_id,
                    FileRecordTable.status == status,
                )

            result = await db_session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.exception(f"获取文件记录失败: {e}")
            raise

    @staticmethod
    async def get_file_record(
        db_session: AsyncSession, session_id: str, filename: str, status: str = "active"
    ) -> Optional[FileRecordTable]:
        """获取单个文件记录"""
        try:
            query = select(FileRecordTable).where(
                FileRecordTable.session_id == session_id,
                FileRecordTable.filename == filename,
                FileRecordTable.status == status,
            )
            result = await db_session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.exception(f"获取文件记录失败: {e}")
            raise

    @staticmethod
    async def get_file_record_by_id(
        db_session: AsyncSession, file_id: int, status: str = "active"
    ) -> Optional[FileRecordTable]:
        """通过ID获取文件记录"""
        try:
            query = select(FileRecordTable).where(
                FileRecordTable.id == file_id, FileRecordTable.status == status
            )
            result = await db_session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.exception(f"通过ID获取文件记录失败: {e}")
            raise

    @staticmethod
    async def update_file_status(
        db_session: AsyncSession,
        session_id: str,
        file_path: str,
        status: str,
        updated_at: datetime = None,
    ) -> bool:
        """更新文件状态"""
        try:
            if not updated_at:
                updated_at = datetime.now()
            stmt = (
                update(FileRecordTable)
                .where(
                    FileRecordTable.session_id == session_id,
                    FileRecordTable.file_path == file_path,
                )
                .values(status=status, updated_at=updated_at)
            )
            await db_session.execute(stmt)
            await db_session.commit()
            return True
        except Exception as e:
            logger.exception(f"更新文件状态失败: {e}")
            await db_session.rollback()
            return False

    @staticmethod
    async def update_file_status_by_id(
        db_session: AsyncSession,
        record_id: int,
        status: str,
        updated_at: datetime = None,
    ) -> bool:
        """通过ID更新文件状态"""
        try:
            if not updated_at:
                updated_at = datetime.now()

            from sqlalchemy import update
            from storage.models import FileRecordTable

            stmt = (
                update(FileRecordTable)
                .where(FileRecordTable.id == record_id)
                .values(status=status, updated_at=updated_at)
            )
            await db_session.execute(stmt)
            await db_session.commit()
            return True
        except Exception as e:
            logger.exception(f"更新文件状态失败: {e}")
            await db_session.rollback()
            return False

    @staticmethod
    async def create_file_upload_error(
        db_session: AsyncSession,
        session_id: str,
        filename: str,
        error_message: str,
        upload_time: datetime = None,
    ) -> FileUploadErrorTable:
        """创建文件上传错误记录"""
        try:
            if not upload_time:
                upload_time = datetime.now()

            error_record = FileUploadErrorTable(
                session_id=session_id,
                filename=filename,
                error_message=error_message,
                upload_time=upload_time,
            )
            db_session.add(error_record)
            await db_session.commit()
            await db_session.refresh(error_record)
            return error_record
        except Exception as e:
            logger.exception(f"创建文件上传错误记录失败: {e}")
            await db_session.rollback()
            raise

    @staticmethod
    async def get_file_list_response(
        db_session: AsyncSession, session_id: str, status: str = "active"
    ) -> FileListResponse:
        """获取文件列表响应"""
        try:
            file_records = await FileService.get_file_records(
                db_session, session_id, status
            )

            # 计算总文件大小
            total_size = sum(record.file_size for record in file_records)

            # 转换为响应格式
            files = [
                FileRecordResponse(
                    id=record.id,
                    session_id=record.session_id,
                    filename=record.filename,
                    original_filename=record.original_filename,
                    file_path=record.file_path,
                    file_size=record.file_size,
                    content_type=record.content_type,
                    upload_time=record.upload_time,
                    status=record.status,
                    file_metadata=record.file_metadata or {},
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                )
                for record in file_records
            ]

            return FileListResponse(
                files=files, total=len(files), total_size=total_size
            )
        except Exception as e:
            logger.exception(f"获取文件列表响应失败: {e}")
            raise

    @staticmethod
    async def check_file_exists(
        db_session: AsyncSession, session_id: str, filename: str
    ) -> bool:
        """检查文件是否存在"""
        try:
            file_record = await FileService.get_file_record(
                db_session, session_id, filename
            )
            if not file_record:
                return False

            # 检查物理文件是否存在
            return os.path.exists(file_record.file_path)
        except Exception as e:
            logger.exception(f"检查文件存在性失败: {e}")
            return False

    @staticmethod
    async def cleanup_orphaned_records(db_session: AsyncSession) -> int:
        """清理孤立的文件记录（物理文件不存在但记录状态为active）"""
        try:
            # 获取所有活跃的文件记录
            active_records = await FileService.get_file_records(db_session, "all")
            active_records = [r for r in active_records if r.status == "active"]

            cleaned_count = 0
            for record in active_records:
                if not os.path.exists(record.file_path):
                    # 标记为已删除
                    await FileService.update_file_status_by_id(
                        db_session, record.id, "deleted"
                    )
                    cleaned_count += 1

            logger.info(f"清理了 {cleaned_count} 个孤立的文件记录")
            return cleaned_count
        except Exception as e:
            logger.exception(f"清理孤立记录失败: {e}")
            raise

    @staticmethod
    async def upload_file(
        db_session: AsyncSession,
        session_id: str,
        state_machine: StateMachine,
        file: UploadFile,
        current_state: StateNode,
    ) -> dict:
        """上传文件"""
        try:
            # 获取上下文
            context = await state_machine.get_context(session_id)

            # 创建上传目录
            upload_dir = os.path.join(settings.app.uploads_dir, session_id)
            os.makedirs(upload_dir, exist_ok=True)

            # 保存文件
            file_path = os.path.join(upload_dir, file.filename)
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)

            file_size = os.path.getsize(file_path)
            upload_time = datetime.now()

            # 创建文件记录
            file_record = await FileService.create_file_record(
                db_session=db_session,
                session_id=session_id,
                filename=file.filename,
                original_filename=file.filename,
                file_path=file_path,
                file_size=file_size,
                content_type=file.content_type,
                upload_time=upload_time,
                metadata={},
            )

            # 添加到上下文
            file_upload_info = FileUpload(
                file_id=file_record.id,
                filename=file.filename,
                file_path=file_path,
                file_size=file_size,
                content_type=file.content_type,
                upload_time=upload_time,
            )
            context.add_uploaded_file(current_state.name, file_upload_info)
            context.add_chat_message(
                role=MessageRole.USER,
                content=f"{file.filename}",
                add_file=file_upload_info,
            )
            await state_machine.context_store.update_context(context)

            # 检查是否还需要更多文件
            file_upload_hint = await FileService.get_file_upload_hint(
                current_state, context
            )

            # 自动验证文件 - 只有在所有必需文件都上传完成后才进行验证
            validation_result = None
            validation_config = default_file_validator.get_validation_config(
                current_state
            )

            if (
                not file_upload_hint
                and validation_config
                and validation_config.auto_validate
            ):
                validation_summary = await default_file_validator.validate_files(
                    session_id, current_state, context
                )
                validation_result = validation_summary

            return {
                "filename": file.filename,
                "file_path": file_path,
                "file_size": file_size,
                "content_type": file.content_type,
                "upload_time": upload_time,
                "file_id": file_record.id,
                "file_upload_hint": file_upload_hint,
                "validation_result": validation_result,
            }

        except Exception as e:
            logger.exception(f"文件上传失败: {e}")
            raise

    @staticmethod
    async def get_file_upload_hint(
        current_state: StateNode, context: FlowContext
    ) -> str:
        """获取文件上传提示信息"""
        try:
            # 获取验证配置
            validation_config = default_file_validator.get_validation_config(
                current_state
            )
            if not validation_config:
                return ""

            # 检查是否已经上传了文件
            all_files = context.uploaded_files.get(current_state.name, [])

            hints = []

            # 检查必需文件是否齐全
            if validation_config.required_files:
                missing_files = []
                for rule in validation_config.required_files:
                    # 检查是否有匹配的文件
                    matching_files = []
                    for file_info in all_files:
                        try:
                            if re.search(
                                rule.name_pattern, file_info.filename, re.IGNORECASE
                            ):
                                matching_files.append(file_info)
                        except re.error:
                            # 如果正则表达式失败，使用简单的字符串匹配
                            if (
                                rule.name_pattern.lower()
                                .replace("*", "")
                                .replace(".", "")
                                in file_info.filename.lower()
                            ):
                                matching_files.append(file_info)

                    # 检查数量是否满足要求
                    if len(matching_files) < rule.min_count:
                        missing_files.append(
                            f"{rule.description} (还需要 {rule.min_count - len(matching_files)} 个), 文件名模式: {rule.name_pattern} "
                        )

                if missing_files:
                    hints.append(f"还需要上传以下文件：{', '.join(missing_files)}")
                else:
                    # 所有必需文件都已上传，不需要提示
                    return ""

            # 添加文件类型建议
            if validation_config.allowed_types:
                type_descriptions = []
                for content_type in validation_config.allowed_types:
                    if content_type.startswith("image/"):
                        type_descriptions.append("图片文件")
                    elif content_type == "application/pdf":
                        type_descriptions.append("PDF文件")
                    elif "word" in content_type:
                        type_descriptions.append("Word文档")

                if type_descriptions:
                    hints.append(f"支持的文件类型：{', '.join(type_descriptions)}")

            # 添加文件大小建议
            if validation_config.max_size_mb:
                hints.append(f"文件大小不能超过 {validation_config.max_size_mb} MB")

            # 添加命名规范建议
            if validation_config.naming_convention:
                hints.append(
                    f"文件名建议遵循以下格式：{validation_config.naming_convention.pattern}"
                )

            if hints:
                hint_text = "\n".join(f"• {hint}" for hint in hints)
                return f"\n\n📎 **文件上传提示**\n{hint_text}\n\n您可以通过以下方式上传文件：\n1. 点击聊天框的附件按钮\n"

            return ""

        except Exception as e:
            logger.warning(f"获取文件上传提示失败: {e}")
            return ""

    @staticmethod
    async def rename_file(
        db_session: AsyncSession,
        session_id: str,
        file_id: int,
        new_filename: str,
        document_type: str,
    ) -> dict:
        """重命名文件"""
        try:
            # 获取上下文
            flow_config = flow_manager.get_flow_by_name("simple_test_flow")
            state_machine = StateMachine(flow_config, db_session=db_session)
            context = await state_machine.get_context(session_id)

            # 通过file_id查找文件记录
            file_record = await FileService.get_file_record_by_id(db_session, file_id)

            if not file_record:
                raise ValueError("文件记录未找到")

            # 查找要重命名的文件
            found_file = None
            for state, files in context.uploaded_files.items():
                for file_info in files:
                    if file_info.filename == file_record.filename:
                        found_file = file_info
                        break

            if not found_file:
                raise ValueError("文件未找到")

            # 构建新文件路径
            old_file_path = found_file.file_path
            new_file_path = os.path.join(os.path.dirname(old_file_path), new_filename)

            # 重命名文件
            if os.path.exists(old_file_path):
                os.rename(old_file_path, new_file_path)

            # 更新文件信息
            found_file.filename = new_filename
            found_file.file_path = new_file_path
            context.add_chat_message(
                role=MessageRole.USER,
                content=f"重命名文件成功，文件名已从 {file_record.filename} 更改为 {new_filename}",
                rename_files=(file_record.filename, new_filename),
            )
            # 更新上下文
            await state_machine.context_store.update_context(context)

            # 更新文件记录状态为updating
            await FileService.update_file_status_by_id(
                db_session,
                record_id=file_id,
                status="updating",
                updated_at=datetime.now(),
            )

            # 更新文件记录（更新文件名和路径）
            from storage.models import FileRecordTable

            stmt = (
                update(FileRecordTable)
                .where(FileRecordTable.id == file_id)
                .values(
                    filename=new_filename,
                    file_path=new_file_path,
                    updated_at=datetime.now(),
                )
            )
            await db_session.execute(stmt)
            await db_session.commit()

            return {
                "old_filename": file_record.filename,
                "new_filename": new_filename,
                "message": f"文件已成功重命名为 {new_filename}",
                "status": "success",
            }

        except Exception as e:
            logger.exception(f"文件重命名失败: {e}")
            raise

    @staticmethod
    async def delete_file(
        db_session: AsyncSession,
        session_id: str,
        file_id: int,
        state_machine: StateMachine,
    ) -> dict:
        """删除文件"""
        try:
            # 通过file_id查找文件记录
            file_record = await FileService.get_file_record_by_id(db_session, file_id)

            if not file_record:
                raise ValueError("文件记录未找到")
            # 获取上下文
            context = await state_machine.get_context(session_id)
            # 查找要删除的文件
            found_file = None
            for state, files in list(context.uploaded_files.items()):
                for i, file_info in enumerate(files):
                    if (
                        file_info.filename == file_record.filename
                        and file_id == file_info.file_id
                    ):
                        # 删除物理文件
                        if os.path.exists(file_info.file_path):
                            os.remove(file_info.file_path)

                        # 从上下文中删除
                        files.pop(i)
                        if not files:
                            del context.uploaded_files[state]

                        found_file = file_info
                        break

                if found_file:
                    break

            if not found_file:
                raise ValueError("文件未找到")
            context.add_chat_message(
                MessageRole.USER,
                f"文件 {file_record.filename} 已成功删除",
                delete_file=found_file,
            )
            # 更新上下文
            await state_machine.context_store.update_context(context)

            # 更新文件记录状态为deleted
            await FileService.update_file_status_by_id(
                db_session,
                record_id=file_id,
                status="deleted",
                updated_at=datetime.now(),
            )

            return {
                "message": f"文件 {file_record.filename} 已成功删除",
                "status": "success",
            }

        except Exception as e:
            logger.exception(f"删除文件失败: {e}")
            raise
