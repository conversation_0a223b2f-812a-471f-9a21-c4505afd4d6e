"""
会话管理服务模块
处理会话相关的数据库操作
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from storage.session_store import SessionStore
from storage.models import SessionTable
from api.schemas import SessionSummaryResponse, PaginatedResponse


class SessionService:
    """会话管理服务类"""

    @staticmethod
    async def create_session(
        db_session: AsyncSession,
        user_id: Optional[str] = None,
        flow_name: Optional[str] = None,
    ) -> SessionTable:
        """创建会话"""
        try:
            session_store = SessionStore(db_session)
            session = await session_store.create_session(user_id, flow_name)
            return session
        except Exception as e:
            logger.exception(f"创建会话失败: {e}")
            raise

    @staticmethod
    async def get_session(
        db_session: AsyncSession, session_id: str
    ) -> Optional[SessionTable]:
        """获取会话"""
        try:
            session_store = SessionStore(db_session)
            session = await session_store.get_session(session_id)
            return session
        except Exception as e:
            logger.exception(f"获取会话失败: {e}")
            raise

    @staticmethod
    async def list_sessions(
        db_session: AsyncSession, page: int = 1, size: int = 20
    ) -> PaginatedResponse:
        """获取会话列表"""
        try:
            session_store = SessionStore(db_session)
            summaries = await session_store.list_sessions()

            start = (page - 1) * size
            end = start + size
            page_items = summaries[start:end]

            return PaginatedResponse.create(
                items=page_items, total=len(summaries), page=page, size=size
            )
        except Exception as e:
            logger.exception(f"获取会话列表失败: {e}")
            raise

    @staticmethod
    async def update_session_status(
        db_session: AsyncSession, session_id: str, status: str
    ) -> bool:
        """更新会话状态"""
        try:
            session_store = SessionStore(db_session)
            await session_store.update_session_status(session_id, status)
            return True
        except Exception as e:
            logger.exception(f"更新会话状态失败: {e}")
            raise

    @staticmethod
    async def delete_session(db_session: AsyncSession, session_id: str) -> bool:
        """删除会话"""
        try:
            session_store = SessionStore(db_session)
            await session_store.delete_session(session_id)
            return True
        except Exception as e:
            logger.exception(f"删除会话失败: {e}")
            raise

    @staticmethod
    async def pause_session(db_session: AsyncSession, session_id: str) -> bool:
        """暂停会话"""
        try:
            await SessionService.update_session_status(db_session, session_id, "paused")
            return True
        except Exception as e:
            logger.exception(f"暂停会话失败: {e}")
            raise

    @staticmethod
    async def resume_session(db_session: AsyncSession, session_id: str) -> bool:
        """恢复会话"""
        try:
            await SessionService.update_session_status(db_session, session_id, "active")
            return True
        except Exception as e:
            logger.exception(f"恢复会话失败: {e}")
            raise
