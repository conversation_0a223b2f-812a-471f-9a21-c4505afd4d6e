# 简化的测试流程配置
flow_name: "simple_test_flow"
description: "xxx公司服务xxx流程"
version: "1.0.0"

states:
  welcome_user:
    name: "welcome_user"
    prompt: "您好！欢迎使用xxx公司服务系统，我可以为您提供以下服务：\n1. 服务A（文档分析）\n2. 服务B（业务咨询）\n3. 服务C（账户管理）\n请问有什么可以帮您？您可以直接描述需求或上传文档。"
    input_type: "text"
    next: "check_registration"

  check_registration:
    name: "check_registration"
    prompt: "您是否已经注册？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "main_process"
      否: "register"

  register:
    name: "register"
    prompt: "请选择注册类型："
    input_type: "choice"
    options: ["个人", "企业"]
    llm_prompt_hint: "从用户输入中判断注册类型是'个人'还是'企业'。如果用户表达了个人身份，选择'个人'；如果表达了公司或组织身份，选择'企业'。"
    next:
      个人: "collect_personal_info"
      企业: "collect_business_info"

  collect_personal_info:
    name: "collect_personal_info"
    prompt: "请提供个人信息："
    input_type: "text"
    llm_prompt_hint: |
      从文本提取结构化信息：
      1. 姓名（中文或英文全名）
      2. 手机号（11位数字）
      3. 邮箱（符合标准邮箱格式）
      缺失时追问具体字段
    requires: ["name", "email", "phone"]
    next: "main_process"

  collect_business_info:
    name: "collect_business_info"
    prompt: "请提供企业信息，包括：\n- 公司全称\n- 主营业务类型\n- 联系人邮箱\n格式示例：\nXX科技有限公司，软件开发，<EMAIL>"
    input_type: "text"
    llm_prompt_hint: |
        提取企业信息字段：
        1. 公司名称（含'公司'或'有限'等关键字）
        2. 业务类型（描述性短语）
        3. 联系人邮箱
    requires: ["company_name", "business_type", "contact_email"]
    next: "main_process"

  main_process:
    name: "main_process"
    prompt: "请选择您需要的服务："
    input_type: "choice"
    options: ["服务A", "服务B", "服务C"]
    next:
      服务A: "service_a"
      服务B: "service_b"
      服务C: "service_c"
      none: "done"

  service_a:
    name: "service_a"
    prompt: "服务A需要您上传相关文档。请上传您的身份证照片和申请表格。"
    input_type: "file"
    file_validation:
      validation_strategy: "basic"
      allowed_types: ["image/jpeg", "image/png", "image/jpg", "application/pdf"]
      max_size_mb: 5
      required_files:
        - name_pattern: ".*身份证.*|.*ID.*"
          description: "身份证照片"
          min_count: 1
        - name_pattern: ".*申请表.*|.*application.*"
          description: "申请表格"
          min_count: 1
      auto_validate: true
    next: "process_service_a"

  process_service_a:
    name: "process_service_a"
    prompt: "正在处理您的服务A申请，请稍候..."
    input_type: "text"
    next: "service_a_completed"

  service_a_completed:
    name: "service_a_completed"
    prompt: "服务A办理已完成！"
    input_type: "text"
    next: "done"

  service_b:
    name: "service_b"
    prompt: "已完成服务B办理"
    input_type: "text"
    next: "done"

  service_c:
    name: "service_c"
    prompt: "已完成服务B办理"
    input_type: "text"
    next: "done"

  done:
    name: "done"
    prompt: "流程已完成，感谢使用！"
    input_type: "text"
    next: null

# 初始状态
initial_state: "welcome_user"

# 终止状态
final_states: ["done"]
