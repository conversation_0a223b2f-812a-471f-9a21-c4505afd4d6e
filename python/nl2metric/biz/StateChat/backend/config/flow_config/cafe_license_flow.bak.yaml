# 咖啡店营业执照申请流程配置
flow_name: "cafe_license_application"
description: "新加坡咖啡店营业执照申请完整流程"
version: "1.0.0"

states:
  welcome_user:
    name: "welcome_user"
    prompt: "您好！我是AI助手，很高兴为您服务。您想申请哪种类型的营业执照？"
    input_type: "text"
    next: "check_acra_registration"

  check_acra_registration:
    name: "check_acra_registration"
    prompt: "您的咖啡店是否已在ACRA（新加坡会计与企业管制局）注册？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "check_commercial_unit"
      否: "register_acra"

  register_acra:
    name: "register_acra"
    prompt: "请先在ACRA注册。请确定企业结构类型：独资经营 (Sole Proprietorship)、合伙经营 (Partnership)、私人有限公司 (Private Limited Company, Pte Ltd)。"
    input_type: "choice"
    options: ["独资经营", "合伙经营", "私人有限公司"]
    next:
      独资经营: "collect_business_info_and_owner"
      合伙经营: "collect_business_info_and_owner"
      私人有限公司: "collect_business_info_and_director"

  collect_business_info_and_owner:
    name: "collect_business_info_and_owner"
    prompt: "我们现在需要收集企业的基本信息：拟用公司名称、业务性质、营业地址、计划开业日期。同时请提供企业负责人信息：全名、身份证/外籍人士身份证号码 (NRIC/FIN)、国籍、联系电话、电子邮件、职位（所有者/董事/股东）。"
    input_type: "text"
    requires: ["proposed_company_name", "business_nature", "business_address", "planned_opening_date", "full_name", "nric_or_fin", "nationality", "contact_phone_number", "email", "position"]
    next: "verify_identity"

  collect_business_info_and_director:
    name: "collect_business_info_and_director"
    prompt: "我们现在需要收集企业的基本信息：拟用公司名称、业务性质、营业地址、计划开业日期。同时请提供企业负责人信息：全名、身份证/外籍人士身份证号码 (NRIC/FIN)、国籍、联系电话、电子邮件、职位（所有者/董事/股东）、初始实缴资本、股份分配。"
    input_type: "file"
    requires: ["proposed_company_name", "business_nature", "business_address", "planned_opening_date", "full_name", "nric_or_fin", "nationality", "contact_phone_number", "email", "position", "initial_capital", "share_distribution"]
    next: "verify_identity"

  verify_identity:
    name: "verify_identity"
    prompt: "为完成注册，需要进行身份验证。请拍摄您的身份证/护照正反两面的照片。如果您以公司名义注册，请拍摄您公司的UEN（统一企业编号）认证文件照片。"
    input_type: "file"
    requires: ["nric_or_passport_photo", "uen_document_photo"]
    next: "confirm_acra_info"

  confirm_acra_info:
    name: "confirm_acra_info"
    prompt: "请核对ACRA注册信息。请确认所有信息是否正确。"
    input_type: "choice"
    options: ["确认", "修改"]
    next:
      确认: "acra_payment"
      修改: "register_acra"

  acra_payment:
    name: "acra_payment"
    prompt: "ACRA收费标准：私人有限公司：S$115；独资经营/合伙经营企业：S$65。您需要立即在线支付吗？"
    input_type: "choice"
    options: ["立即支付", "稍后支付"]
    next:
      立即支付: "check_commercial_unit"
      稍后支付: "check_commercial_unit"

  check_commercial_unit:
    name: "check_commercial_unit"
    prompt: "您是否已经租用了商业单位？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "provide_commercial_unit_info"
      否: "find_commercial_unit"

  find_commercial_unit:
    name: "find_commercial_unit"
    prompt: "请注意：咖啡馆必须位于市区重建局 (URA) 分区中指定为餐饮用途的区域。点击此处搜索商业地产。一旦您确定了单位，请提供店铺营业地址，上传租赁协议副本以及店铺的平面图/布局图。"
    input_type: "file"
    requires: ["unit_address", "tenancy_agreement_copy", "floor_plan"]
    next: "check_business_services"

  provide_commercial_unit_info:
    name: "provide_commercial_unit_info"
    prompt: "请提供店铺营业地址，上传租赁协议副本以及店铺的平面图/布局图。"
    input_type: "file"
    requires: ["unit_address", "tenancy_agreement_copy", "floor_plan"]
    next: "check_business_services"

  check_business_services:
    name: "check_business_services"
    prompt: "您的咖啡店是否会涉及以下服务？（多选）销售酒精饮料、允许宠物进入、设置户外座位。"
    input_type: "choice" # todo 多选
    options: ["销售酒精饮料", "允许宠物进入", "设置户外座位", "不涉及"]
    next:
      销售酒精饮料: "liquor_license_flow"
      允许宠物进入: "pet_cafe_flow"
      设置户外座位: "outdoor_seating_flow"
      "不涉及": "check_renovation"  # 无选择时

  liquor_license_flow:
    name: "liquor_license_flow"
    prompt: "让我们开始酒类许可证注册。您的咖啡店是允许顾客在店内饮酒还是仅限外卖？您是否会在晚上10:30之后提供或销售酒精饮料？根据您提供的信息，我们将选择一个许可证类别。请为您的酒牌申请拍摄以下文件：ACRA商业档案 (BizFile, UEN)、平面图（显示酒精饮品供应区域）、含酒精饮料的菜单、租赁协议。"
    input_type: "file"
    requires: ["acra_bizfile_uen", "floor_plan_alcohol", "menu_alcohol", "tenancy_agreement"]
    next: "liquor_license_submit"

  liquor_license_submit:
    name: "liquor_license_submit"
    prompt: "您希望我现在提交您的酒类许可证申请，还是下载填写完整的表格包供您审核？"
    input_type: "choice"
    options: ["提交", "下载"]
    next:
      提交: "check_renovation"
      下载: "check_renovation"

  pet_cafe_flow:
    name: "pet_cafe_flow"
    prompt: "让我们开始宠物友好许可证注册。您会为宠物提供食物，还是只允许它们在户外区域？您会有常驻宠物吗？"
    input_type: "choice"
    options: ["为宠物提供食物", "仅限户外区域", "有常驻宠物"]
    next:
      为宠物提供食物: "pet_cafe_license_application"
      仅限户外区域: "hygiene_compliance_upload"
      有常驻宠物: "nparks_animal_display_approval"

  hygiene_compliance_upload:
    name: "hygiene_compliance_upload"
    prompt: "不需要特殊许可证，但您的咖啡馆必须遵守新加坡食品局 (SFA) 的卫生和标识规定。请为卫生合规记录拍摄以下文件：户外座位布局图、宠物区域标识（如有）。"
    input_type: "file"
    requires: ["outdoor_seating_layout", "pet_zone_signage"]
    next: "check_renovation"

  pet_cafe_license_application:
    name: "pet_cafe_license_application"
    prompt: "因为您将为宠物提供食物，您必须向新加坡食品局 (SFA) 申请宠物咖啡馆许可证。请拍摄或上传：宠物菜单（或计划项目）、厨房区域和宠物区域明确分隔的平面布局图、宠物与人类餐具的洗碗区、清洁时间表、租赁协议。"
    input_type: "file"
    requires: ["pet_menu", "floor_plan_pet", "dishwashing_area_layout", "cleaning_schedule", "tenancy_agreement"]
    next: "check_renovation"

  nparks_animal_display_approval:
    name: "nparks_animal_display_approval"
    prompt: "如果您计划饲养或展示常驻宠物作为咖啡馆体验的一部分，需要获得国家公园局 (NParks) 的批准。请拍摄或上传：动物照片及品种、带有宠物围栏/休息区的平面图、健康证明/疫苗接种记录、员工宠物处理标准操作程序 (SOP)。"
    input_type: "file"
    requires: ["animal_photo_breed", "floor_plan_enclosure", "health_certificate_vaccination", "staff_pet_handling_sop"]
    next: "check_renovation"

  outdoor_seating_flow:
    name: "outdoor_seating_flow"
    prompt: "让我们开始露天餐饮区 (ORA) 许可证注册。户外桌椅会设置在公共土地上吗——例如人行道、路边或您店铺前的露天区域？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "ora_permit_application"
      否: "ora_compliance_check"

  ora_permit_application:
    name: "ora_permit_application"
    prompt: "由于您的桌椅将放置在公共土地上，您需要向陆路交通管理局 (LTA) 或相关市镇理事会申请露天餐饮区 (ORA) 许可证。请拍摄或上传：清晰显示室内和室外座位的平面图、拟议户外区域的照片（从街道层面拍摄）、租赁协议（用于核实边界）、业主或商场管理处的批准（如适用）。"
    input_type: "file"
    requires: ["floor_plan_ora", "outdoor_area_photo", "tenancy_agreement", "landlord_approval"]
    next: "check_renovation"

  ora_compliance_check:
    name: "ora_compliance_check"
    prompt: "很好！如果您的户外空间是您租约的一部分，并且在市区重建局 (URA) 批准的边界内，则不需要单独的ORA许可证。我们只需将其记录在您的食品店许可证中。请拍摄或上传：实际户外座位区照片、清晰显示户外区域的平面布局图。"
    input_type: "file"
    requires: ["outdoor_seating_photo", "floor_plan_outdoor"]
    next: "check_renovation"

  check_renovation:
    name: "check_renovation"
    prompt: "您是否对咖啡馆单位进行过任何装修——例如墙体改动、厨房排烟、管道或消防安全系统？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "fsc_application"
      否: "check_signboard"

  fsc_application:
    name: "fsc_application"
    prompt: "请上传所需文件。所需文件：装修布局图/平面图、消防系统计划（如有）、合格人员 (Qualified Person, QP) 详细信息、新加坡民防部队 (SCDF) 临时防火计划 (TFP) 或之前的提交文件（可选）。您的装修完成了吗？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "fsc_submit_or_download"
      否: "tfp_submit_or_download"

  fsc_submit_or_download:
    name: "fsc_submit_or_download"
    prompt: "您希望我将您的消防安全证书 (FSC) 申请提交给新加坡民防部队 (SCDF)，还是下载填写完整的表格包供您提交？"
    input_type: "choice"
    options: ["提交", "下载"]
    next:
      提交: "check_signboard"
      下载: "check_signboard"

  tfp_submit_or_download:
    name: "tfp_submit_or_download"
    prompt: "您希望我将您的临时防火计划 (TFP) 申请提交给新加坡民防部队 (SCDF)，还是下载填写完整的表格包供您提交？"
    input_type: "choice"
    options: ["提交", "下载"]
    next:
      提交: "check_signboard"
      下载: "check_signboard"

  check_signboard:
    name: "check_signboard"
    prompt: "您的咖啡馆是否会有任何外部招牌或店铺标识——例如名称招牌、灯箱或玻璃贴花？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "signboard_license_flow"
      否: "check_hiring"

  signboard_license_flow:
    name: "signboard_license_flow"
    prompt: "请拍摄或上传所需文件。所需文件：招牌设计效果图、建筑立面照片、租赁协议、业主或商场管理处的批准（如适用）。"
    input_type: "file"
    requires: ["signboard_design_mockup", "building_facade_photo", "tenancy_agreement", "landlord_approval"]
    next: "signboard_submit_or_download"

  signboard_submit_or_download:
    name: "signboard_submit_or_download"
    prompt: "您希望我现在提交您的招牌许可证申请，还是下载填写好的表格和附件供您提交？"
    input_type: "choice"
    options: ["提交", "下载"]
    next:
      提交: "check_hiring"
      下载: "check_hiring"

  check_hiring:
    name: "check_hiring"
    prompt: "您会为您的咖啡馆雇佣任何员工吗？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "cpf_registration_flow"
      否: "check_food_shop_license"

  cpf_registration_flow:
    name: "cpf_registration_flow"
    prompt: "请拍摄或提供以下信息以完成您的公积金 (CPF) 注册：ACRA商业档案 (BizFile, UEN)、董事的身份证/外籍人士身份证号码 (NRIC/FIN)、联系信息、银行详细信息（可选）。"
    input_type: "file"
    requires: ["acra_bizfile_uen", "director_nric_fin", "contact_info", "bank_details"]
    next: "cpf_submit_or_download"

  cpf_submit_or_download:
    name: "cpf_submit_or_download"
    prompt: "您希望我现在提交您的公积金雇主申请，还是下载完整的表格包和GIRO表格供您提交？"
    input_type: "choice"
    options: ["提交", "下载"]
    next:
      提交: "check_food_shop_license"
      下载: "check_food_shop_license"

  check_food_shop_license:
    name: "check_food_shop_license"
    prompt: "您是否已申请新加坡食品局 (SFA) 的食品店许可证？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "check_financial_setup"
      否: "food_shop_license_flow"

  food_shop_license_flow:
    name: "food_shop_license_flow"
    prompt: "请拍摄或上传以下所需文件：平面图/布局图、租赁协议、清洁时间表、厨房或内部工程进行中的照片（可选但理想）。同时，请拍摄您员工持有的任何食品卫生证书。"
    input_type: "file"
    requires: ["floor_plan_layout", "tenancy_agreement", "cleaning_schedule", "kitchen_photos", "food_hygiene_certificate"]
    next: "food_shop_submit_or_download"

  food_shop_submit_or_download:
    name: "food_shop_submit_or_download"
    prompt: "您是想现在提交您的食品店许可证申请，还是下载完整的表格和附件作为一个文件包？"
    input_type: "choice"
    options: ["提交", "下载"]
    next:
      提交: "check_financial_setup"
      下载: "check_financial_setup"

  check_financial_setup:
    name: "check_financial_setup"
    prompt: "让我们来准备您的财务设置。您是否已开设了企业银行账户？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "check_gst"
      否: "open_business_bank_account"

  open_business_bank_account:
    name: "open_business_bank_account"
    prompt: "请准备并提供开设公司银行账户的相应材料：ACRA商业档案 / UEN注册文件、负责人身份证明（身份证/外籍人士身份证）、公司章程（仅限私人有限公司）、地址证明。"
    input_type: "file"
    requires: ["acra_bizfile_uen", "responsible_person_id", "articles_of_association", "proof_of_address"]
    next: "check_gst"

  check_gst:
    name: "check_gst"
    prompt: "您预计您的年收入会超过100万新元吗？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "gst_registration"
      否: "bookkeeping_setup"

  gst_registration:
    name: "gst_registration"
    prompt: "请提供商业计划或财务预测，以支持预计收入超过限额的合理性。"
    input_type: "file"
    requires: ["business_plan_or_financial_forecast"]
    next: "bookkeeping_setup"

  bookkeeping_setup:
    name: "bookkeeping_setup"
    prompt: "您稍后是否需要帮助跟踪收入/支出？我们可以帮助您设置簿记或推荐外部会计软件，如Xero、QuickBooks和Talenox。"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "done"
      否: "done"

  done:
    name: "done"
    prompt: "所有步骤已完成。申请结果将在N个工作日内通过电子邮件回复给您。感谢您的关注和使用。"
    input_type: "text"
    next: null

# 初始状态
initial_state: "welcome_user"

# 终止状态
final_states: ["done"]
