# Unified Application Configuration

# Application Core Settings
app:
  # File upload settings
  uploads_dir: "uploads"

  # Flow management
  default_flow_name: "simple_test_flow"

  # Server settings
  host: "0.0.0.0"
  port: 5432

  # Environment setting (development, test, production)
  environment: "development"

# API Client Settings (for testing and external integrations)
api:
  base_url: "http://localhost:5432"
  timeout: 30
  max_retries: 3

# General application settings
logging:
  level: "INFO"
  request_logging: true
  response_logging: false  # Avoid logging sensitive info from OpenAI
  log_dir: "logs"

# Database Configuration
database:
  development:
    type: sqlite
    database: data/state_chat.db
    echo: true

  test:
    type: sqlite
    database: ":memory:"  # In-memory database for tests
    echo: true

  production:
    type: mysql
    host: ${DB_HOST}
    port: ${DB_PORT:-3306}
    database: ${DB_NAME}
    user: ${DB_USER}
    password: ${DB_PASSWORD}
    charset: utf8mb4
    pool_size: 20
    max_overflow: 10
    timeout: 30
    recycle: 3600
    pre_ping: true
    echo: false

  migrations:
    directory: migrations
    auto_migrate: true
    check_version: true

# OpenAI Configuration
openai:
  api:
#    base_url: "http://**************:16701/v1"
    base_url: "https://api.deepseek.com/"
    api_key: "***********************************" # Should be overridden by env var in prod
    organization: "${OPENAI_ORG_ID}"

  models:
    chat:
      name: "deepseek-chat"
#      name: "qwen3_32b"
      temperature: 0.01
      max_tokens: 4096
      top_p: 0.01
      max_retries: 3
      request_timeout: 30
      frequency_penalty: 0.0
      presence_penalty: 0.0
      extra_body:
        chat_template_kwargs:
          enable_thinking: False

      system_prompt: |
        你是一个友好的AI助手，负责与用户进行日常对话。
        请保持对话轻松愉快，同时专业且有帮助。
        如果涉及到具体的业务问题，建议用户通过正式流程办理。

    intent:
      name: "deepseek-chat"
#      name: "qwen3_32b"
      temperature: 0.01
      max_tokens: 500
      top_p: 0.01
      max_retries: 3
      request_timeout: 30
      frequency_penalty: 0.0
      presence_penalty: 0.0
      extra_body:
        chat_template_kwargs:
          enable_thinking: False

  prompts:
    llm_choice_template: "prompts/llm_choice_template.jinja2"
    parameter_extraction_template: "prompts/parameter_extraction_template.jinja2"
    business_response_template: "prompts/business_response_template.jinja2"
    intent_template: "prompts/intent_template.jinja2"
    file_upload_hint_template: "prompts/file_upload_hint_template.jinja2"
    chitchat_template: "prompts/chitchat.jinja2"