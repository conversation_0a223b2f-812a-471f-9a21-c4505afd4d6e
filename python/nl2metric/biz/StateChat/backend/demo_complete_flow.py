"""
完整的智能客服流程演示
"""

import asyncio
from test_mock_chat import MockChatManager


async def demo_complete_flow():
    """演示完整的智能客服流程"""
    print("=== 智能客服完整流程演示 ===\n")

    # 初始化管理器
    manager = MockChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"demo_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    print("开始新的客服会话...")
    print("=" * 60)

    # 完整的对话流程
    conversation = [
        "你好",
        "否",
        "个人",
        "我叫张三，我的邮箱是********************，手机号是13800138000",
        "服务A",
    ]

    for i, message in enumerate(conversation, 1):
        print(f"\n【第{i}轮对话】")
        print(f"用户：{message}")

        result = await manager.handle_message(session_id, message)

        print(f"\n客服：{result['response']}")
        print(f"[当前状态：{result['current_state']}]")

        if result["user_data"]:
            print(
                f"[已收集信息：{', '.join(f'{k}={v}' for k, v in result['user_data'].items())}]"
            )

        print("-" * 60)

        await asyncio.sleep(1)

    # 显示最终会话状态
    session_info = manager.get_session_info(session_id)
    print(f"\n=== 会话总结 ===")
    print(f"会话ID：{session_id}")
    print(f"最终状态：{session_info['current_state']}")
    print(f"是否完成：{session_info['is_complete']}")
    print(f"收集的用户数据：{session_info['user_data']}")
    print(f"总消息数：{session_info['message_count']}")

    # 显示完整对话历史
    print(f"\n=== 完整对话历史 ===")
    state = manager.sessions[session_id]
    for i, msg in enumerate(state.messages, 1):
        role = "用户" if msg["role"] == "user" else "客服"
        print(f"{i}. {role}：{msg['content']}")


if __name__ == "__main__":
    from datetime import datetime

    asyncio.run(demo_complete_flow())
