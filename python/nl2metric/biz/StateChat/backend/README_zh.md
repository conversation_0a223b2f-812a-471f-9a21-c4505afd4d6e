# 智能客服聊天流程 - LangGraph 实现

本项目基于 LangGraph 实现了一个 AI 驱动的智能客服聊天流程，支持复杂的状态管理、智能意图识别、结构化数据提取和文件验证。

## 功能特性

### 1. 基于 LangGraph 的状态管理
- 使用 YAML 配置文件定义流程状态和转移逻辑
- 支持条件转移和自动状态转换
- 灵活的状态节点配置

### 2. 智能 LLM 集成
- 意图识别和分类
- 结构化数据提取
- 智能追问机制
- 数据质量验证

### 3. 文件处理和验证
- 支持多种文件类型验证
- 文件名模式匹配
- 文件大小限制检查
- 自动文件分类

### 4. FastAPI 集成
- RESTful API 接口
- 文件上传支持
- 会话管理
- 健康检查

## 文件结构

```
backend/
├── langgraph_chat_flow.py      # 核心 LangGraph 实现
├── enhanced_llm_processor.py   # 增强 LLM 处理器
├── file_validator.py          # 文件验证模块
├── fastapi_integration.py     # FastAPI 集成
├── test_enhanced_chat.py      # 测试和示例
├── setup_deployment.py        # 部署设置脚本
├── config/
│   └── flow_config/
│       └── simple_test_flow.yaml  # 流程配置文件
├── requirements.txt           # 依赖项
├── start.sh                   # 启动脚本
├── Dockerfile                # Docker 配置
└── docker-compose.yml        # Docker Compose 配置
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 设置环境变量
```bash
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_BASE_URL="https://api.openai.com/v1"
```

### 3. 运行测试
```bash
python test_enhanced_chat.py
```

### 4. 启动服务
```bash
# 方式1: 直接运行
python fastapi_integration.py

# 方式2: 使用启动脚本
bash start.sh

# 方式3: 使用 Docker
docker-compose up
```

## API 使用

### 发送消息
```bash
curl -X POST "http://localhost:8000/chat/message" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "user123",
    "message": "你好"
  }'
```

### 上传文件
```bash
curl -X POST "http://localhost:8000/chat/upload" \
  -F "session_id=user123" \
  -F "files=@file1.jpg" \
  -F "files=@file2.pdf" \
  -F 'validation_config={"validation_strategy": "basic", "allowed_types": ["image/jpeg", "application/pdf"]}'
```

### 获取会话信息
```bash
curl -X GET "http://localhost:8000/chat/session/user123"
```

## 配置说明

流程配置使用 YAML 格式，包含以下主要部分：

```yaml
flow_name: "流程名称"
states:
  state_name:
    name: "状态名称"
    prompt: "提示文本"
    input_type: "text|choice|file"
    next: "下一个状态"
    # 可选配置
    options: ["选项1", "选项2"]
    requires: ["字段1", "字段2"]
    llm_prompt_hint: "LLM提示"
    file_validation:
      allowed_types: ["image/jpeg"]
      max_size_mb: 5
```

## 扩展指南

### 1. 添加新的状态节点
在 YAML 配置文件中添加新的状态定义，系统会自动加载。

### 2. 自定义文件验证
继承 `FileValidator` 类，实现自定义的验证逻辑。

### 3. 集成其他 LLM
修改 `EnhancedLLMProcessor` 中的模型配置，支持不同的 LLM 提供商。

### 4. 添加持久化存储
集成数据库，实现会话数据的持久化存储。

## 示例流程

系统包含一个完整的示例流程，演示了：
- 用户欢迎
- 注册状态检查
- 个人/企业信息收集
- 服务选择
- 文件上传验证
- 流程完成

## 部署

### 本地部署
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python fastapi_integration.py
```

### Docker 部署
```bash
# 构建镜像
docker build -t state-chat .

# 运行容器
docker run -p 8000:8000 -e OPENAI_API_KEY=your-key state-chat
```

### Docker Compose
```bash
docker-compose up -d
```

## 注意事项

1. 确保 OpenAI API 密钥已正确设置
2. 文件上传目录需要适当的权限
3. 生产环境建议使用 HTTPS
4. 根据实际需求调整 LLM 模型参数