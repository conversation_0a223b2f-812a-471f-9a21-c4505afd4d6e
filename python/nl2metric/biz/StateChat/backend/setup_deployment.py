"""
依赖项和启动脚本
"""

# requirements.txt 内容
requirements = """
langgraph>=0.0.40
langchain>=0.0.340
langchain-openai>=0.0.2
pydantic>=2.0.0
pyyaml>=6.0
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6
aiofiles>=23.0.0
"""

# 创建 requirements.txt 文件
with open("requirements.txt", "w") as f:
    f.write(requirements.strip())

# 创建启动脚本
start_script = """#!/bin/bash

# 启动脚本
echo "Starting State Chat Service..."

# 设置环境变量
export OPENAI_API_KEY=${OPENAI_API_KEY:-"your-openai-api-key"}
export OPENAI_BASE_URL=${OPENAI_BASE_URL:-"https://api.openai.com/v1"}

# 安装依赖
echo "Installing dependencies..."
pip install -r requirements.txt

# 启动服务
echo "Starting FastAPI server..."
python fastapi_integration.py
"""

with open("start.sh", "w") as f:
    f.write(start_script)

# 创建 Dockerfile
dockerfile = """FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "fastapi_integration.py"]
"""

with open("Dockerfile", "w") as f:
    f.write(dockerfile)

# 创建 docker-compose.yml
docker_compose = """version: '3.8'

services:
  state-chat:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
    volumes:
      - ./config:/app/config
      - ./uploads:/app/uploads
    restart: unless-stopped
"""

with open("docker-compose.yml", "w") as f:
    f.write(docker_compose)

print("Created required files:")
print("- requirements.txt")
print("- start.sh")
print("- Dockerfile")
print("- docker-compose.yml")
print("\nTo run the service:")
print("1. Set OPENAI_API_KEY environment variable")
print("2. Run: bash start.sh")
print("3. Or use Docker: docker-compose up")
