"""
模拟测试版本 - 无需API密钥
"""

import asyncio
import json
import re
import yaml
from typing import Dict, List, Any, Optional
from datetime import datetime


class MockChatState:
    """模拟聊天状态"""

    def __init__(self, current_state: str):
        self.current_state = current_state
        self.messages = []
        self.user_data = {}
        self.is_complete = False


class MockStateChatFlow:
    """模拟状态聊天流程"""

    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.states = self.config["states"]

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载流程配置"""
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    async def process_message(
        self, state: MockChatState, message: str
    ) -> MockChatState:
        """处理用户消息"""
        # 添加用户消息
        state.messages.append({"role": "user", "content": message})

        # 获取当前状态配置
        current_config = self.states.get(state.current_state)
        if not current_config:
            state.messages.append({"role": "assistant", "content": "错误：无效的状态"})
            return state

        # 根据输入类型处理
        if current_config["input_type"] == "choice":
            next_state = self._process_choice(message, current_config)
            if not next_state:
                # 没有匹配的选择，提示用户
                options = current_config.get("options", [])
                state.messages.append(
                    {
                        "role": "assistant",
                        "content": f"请选择有效选项：{', '.join(options)}",
                    }
                )
                return state
        elif current_config["input_type"] == "text":
            next_state = await self._process_text(state, message, current_config)
        else:
            next_state = current_config.get("next")

        # 更新状态
        if next_state:
            state.current_state = next_state
            next_config = self.states.get(next_state)

            if next_config:
                # 添加AI响应
                ai_response = next_config["prompt"]
                state.messages.append({"role": "assistant", "content": ai_response})

                # 检查是否是终止状态
                if next_state in self.config.get("final_states", []):
                    state.is_complete = True

        return state

    def _process_choice(self, message: str, config: Dict[str, Any]) -> Optional[str]:
        """处理选择输入"""
        options = config.get("options", [])
        next_mapping = config.get("next", {})

        # 简单匹配
        message_lower = message.lower()
        for option in options:
            if option.lower() in message_lower:
                return next_mapping.get(option)

        # 如果没有匹配，返回None（保持当前状态）
        return None

    async def _process_text(
        self, state: MockChatState, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """处理文本输入"""
        next_state = config.get("next")

        # 如果需要提取字段
        if "requires" in config:
            required_fields = config["requires"]
            extracted_data = self._extract_fields(message, required_fields)

            # 检查是否所有必需字段都已提取
            missing_fields = [
                field for field in required_fields if not extracted_data.get(field)
            ]

            if missing_fields:
                # 生成追问
                follow_up = self._generate_follow_up(missing_fields)
                state.messages.append({"role": "assistant", "content": follow_up})

                # 保存已提取的数据
                state.user_data.update({k: v for k, v in extracted_data.items() if v})

                return None  # 保持当前状态
            else:
                # 所有字段都已提供，保存数据
                state.user_data.update(extracted_data)

        return next_state

    def _extract_fields(self, text: str, required_fields: List[str]) -> Dict[str, Any]:
        """从文本中提取字段"""
        data = {}

        # 提取邮箱
        email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
        email_match = re.search(email_pattern, text)
        if email_match:
            data["email"] = email_match.group()

        # 提取手机号
        phone_pattern = r"1[3-9]\d{9}"
        phone_match = re.search(phone_pattern, text)
        if phone_match:
            data["phone"] = phone_match.group()

        # 提取姓名（简单模式）
        if "name" in required_fields:
            name_patterns = [r"我叫([^，。\s]+)", r"姓名[：:]\s*([^，。\s]+)"]
            for pattern in name_patterns:
                name_match = re.search(pattern, text)
                if name_match and len(name_match.group(1)) >= 2:
                    data["name"] = name_match.group(1)
                    break

        return data

    def _generate_follow_up(self, missing_fields: List[str]) -> str:
        """生成追问问题"""
        field_prompts = {
            "name": "请提供您的姓名",
            "email": "请提供您的邮箱地址",
            "phone": "请提供您的手机号码",
            "company_name": "请提供您的公司名称",
            "business_type": "请描述您的业务类型",
            "contact_email": "请提供联系人邮箱",
        }

        prompts = [
            field_prompts.get(field, f"请提供{field}") for field in missing_fields
        ]
        return "请补充以下信息：" + "、".join(prompts)


class MockChatManager:
    """模拟聊天管理器"""

    def __init__(self, config_path: str):
        self.flow = MockStateChatFlow(config_path)
        self.sessions: Dict[str, MockChatState] = {}

    async def handle_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        # 获取或创建会话
        if session_id not in self.sessions:
            self.sessions[session_id] = MockChatState(
                current_state=self.flow.config["initial_state"]
            )

            # 添加欢迎消息
            initial_config = self.flow.states[self.flow.config["initial_state"]]
            self.sessions[session_id].messages.append(
                {"role": "assistant", "content": initial_config["prompt"]}
            )

        state = self.sessions[session_id]

        # 处理消息
        new_state = await self.flow.process_message(state, message)
        self.sessions[session_id] = new_state

        # 返回最新响应
        latest_message = new_state.messages[-1] if new_state.messages else {}

        return {
            "response": latest_message.get("content", ""),
            "current_state": new_state.current_state,
            "user_data": new_state.user_data,
            "is_complete": new_state.is_complete,
            "messages": new_state.messages,
        }

    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        if session_id in self.sessions:
            state = self.sessions[session_id]
            return {
                "current_state": state.current_state,
                "user_data": state.user_data,
                "message_count": len(state.messages),
                "is_complete": state.is_complete,
            }
        return {}

    def reset_session(self, session_id: str):
        """重置会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]


async def test_mock_flow():
    """测试模拟流程"""
    print("=== 测试模拟智能客服流程 ===\n")

    # 初始化管理器
    manager = MockChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # 模拟对话流程
    test_scenarios = [
        {
            "message": "你好",
            "expected_state": "check_registration",
            "description": "欢迎用户",
        },
        {
            "message": "我还没有注册",
            "expected_state": "register",
            "description": "用户选择未注册",
        },
        {
            "message": "个人",
            "expected_state": "collect_personal_info",
            "description": "选择个人注册",
        },
        {
            "message": "我叫李四，我的邮箱是****************",
            "expected_state": "collect_personal_info",
            "description": "提供部分个人信息（缺失手机号）",
        },
        {
            "message": "我的手机号是13900139000",
            "expected_state": "main_process",
            "description": "补充完整信息",
        },
        {
            "message": "我想办理服务A",
            "expected_state": "service_a",
            "description": "选择服务A",
        },
    ]

    for i, scenario in enumerate(test_scenarios, 1):
        print(f"--- 步骤 {i}: {scenario['description']} ---")
        print(f"用户输入: {scenario['message']}")

        result = await manager.handle_message(session_id, scenario["message"])

        print(f"AI回复: {result['response']}")
        print(f"当前状态: {result['current_state']}")
        print(f"收集的数据: {result['user_data']}")
        print(f"预期状态: {scenario['expected_state']}")
        print(
            f"状态匹配: {'✓' if result['current_state'] == scenario['expected_state'] else '✗'}"
        )
        print(f"是否完成: {result['is_complete']}")
        print("\n" + "=" * 50 + "\n")

        # 短暂延迟
        await asyncio.sleep(0.5)

    # 获取会话总结
    session_info = manager.get_session_info(session_id)
    print("=== 会话总结 ===")
    print(f"会话ID: {session_id}")
    print(f"当前状态: {session_info.get('current_state')}")
    print(f"用户数据: {session_info.get('user_data')}")
    print(f"消息数量: {session_info.get('message_count')}")

    # 打印完整对话历史
    print("\n=== 完整对话历史 ===")
    state = manager.sessions[session_id]
    for msg in state.messages:
        print(f"{msg['role']}: {msg['content']}")


if __name__ == "__main__":
    asyncio.run(test_mock_flow())
