"""
文件验证模块
"""

import re
import mimetypes
from typing import List, Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, validator


class FileValidationRule(BaseModel):
    """文件验证规则"""

    name_pattern: str
    description: str
    min_count: int = 1
    max_count: Optional[int] = None


class FileValidationConfig(BaseModel):
    """文件验证配置"""

    validation_strategy: str = "basic"
    allowed_types: List[str] = []
    max_size_mb: int = 10
    required_files: List[FileValidationRule] = []
    auto_validate: bool = True


class FileValidator:
    """文件验证器"""

    def __init__(self, config: FileValidationConfig):
        self.config = config

    def validate_file(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """验证单个文件"""
        errors = []

        # 检查文件类型
        if self.config.allowed_types:
            content_type = file_info.get("content_type", "")
            if content_type not in self.config.allowed_types:
                errors.append(f"不支持的文件类型: {content_type}")

        # 检查文件大小
        max_size_bytes = self.config.max_size_mb * 1024 * 1024
        if file_info.get("size", 0) > max_size_bytes:
            errors.append(f"文件大小超过限制（最大 {self.config.max_size_mb}MB）")

        return {"is_valid": len(errors) == 0, "errors": errors, "file_info": file_info}

    def validate_files(self, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证文件集合"""
        result = {
            "is_valid": True,
            "errors": [],
            "validated_files": [],
            "missing_requirements": [],
        }

        # 验证每个文件
        for file_info in files:
            validation = self.validate_file(file_info)
            if validation["is_valid"]:
                result["validated_files"].append(file_info)
            else:
                result["errors"].extend(validation["errors"])

        # 检查必需文件
        for rule in self.config.required_files:
            matched_files = [
                f
                for f in result["validated_files"]
                if re.search(rule.name_pattern, f.get("filename", ""), re.IGNORECASE)
            ]

            if len(matched_files) < rule.min_count:
                result["missing_requirements"].append(
                    {
                        "rule": rule,
                        "message": f"需要至少 {rule.min_count} 个{rule.description}文件",
                    }
                )
                result["is_valid"] = False

            if rule.max_count and len(matched_files) > rule.max_count:
                result["errors"].append(
                    f"{rule.description}文件数量超过限制（最多 {rule.max_count} 个）"
                )
                result["is_valid"] = False

        return result

    def extract_file_info(self, file_path: str) -> Dict[str, Any]:
        """提取文件信息"""
        path = Path(file_path)

        if not path.exists():
            return {"error": "文件不存在"}

        # 获取文件大小
        size = path.stat().st_size

        # 获取文件类型
        content_type, _ = mimetypes.guess_type(str(path))
        if not content_type:
            content_type = "application/octet-stream"

        return {
            "filename": path.name,
            "content_type": content_type,
            "size": size,
            "path": str(path),
        }
