"""
简化的状态聊天流程实现 - 修复版本
"""

import asyncio
import json
import re
import yaml
from typing import Dict, List, Any, Optional, Union, Literal
from datetime import datetime
from pathlib import Path

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field


class SimpleChatState(BaseModel):
    """简化的聊天状态"""

    current_state: str
    messages: List[Dict[str, str]] = []
    user_data: Dict[str, Any] = {}
    collected_files: List[Dict[str, Any]] = []
    is_complete: bool = False


class SimpleStateChatFlow:
    """简化的状态聊天流程"""

    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.llm = ChatOpenAI(
            model="gpt-3.5-turbo-1106", temperature=0.1, streaming=False
        )
        self.states = self.config["states"]

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载流程配置"""
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    async def process_message(
        self, state: SimpleChatState, message: str
    ) -> SimpleChatState:
        """处理用户消息"""
        # 添加用户消息
        state.messages.append({"role": "user", "content": message})

        # 获取当前状态配置
        current_config = self.states.get(state.current_state)
        if not current_config:
            state.messages.append({"role": "assistant", "content": "错误：无效的状态"})
            return state

        # 根据输入类型处理
        if current_config["input_type"] == "choice":
            next_state = await self._process_choice(state, message, current_config)
        elif current_config["input_type"] == "text":
            next_state = await self._process_text(state, message, current_config)
        elif current_config["input_type"] == "file":
            next_state = await self._process_file(state, message, current_config)
        else:
            next_state = current_config.get("next")

        # 更新状态
        if next_state:
            state.current_state = next_state
            next_config = self.states.get(next_state)

            if next_config:
                # 添加AI响应
                ai_response = next_config["prompt"]
                state.messages.append({"role": "assistant", "content": ai_response})

                # 检查是否是终止状态
                if next_state in self.config.get("final_states", []):
                    state.is_complete = True

        return state

    async def _process_choice(
        self, state: SimpleChatState, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """处理选择输入"""
        options = config.get("options", [])
        next_mapping = config.get("next", {})

        # 如果有LLM提示，使用LLM解析
        if "llm_prompt_hint" in config:
            prompt = f"""
            {config["llm_prompt_hint"]}
            
            用户输入: {message}
            可选选项: {", ".join(options)}
            
            请直接返回最匹配的选项。
            """

            response = await self.llm.ainvoke(
                [SystemMessage(content=prompt), HumanMessage(content=message)]
            )

            choice = response.content.strip()
            # 尝试匹配选项
            for option in options:
                if option.lower() in choice.lower() or choice.lower() in option.lower():
                    return next_mapping.get(option)

        # 直接匹配
        for option in options:
            if option.lower() in message.lower():
                return next_mapping.get(option)

        # 如果没有匹配，返回None（保持当前状态）
        state.messages.append(
            {"role": "assistant", "content": f"请选择有效选项：{', '.join(options)}"}
        )
        return None

    async def _process_text(
        self, state: SimpleChatState, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """处理文本输入"""
        next_state = config.get("next")

        # 如果需要提取字段
        if "requires" in config:
            required_fields = config["requires"]
            extracted_data = await self._extract_fields(
                message, required_fields, config.get("llm_prompt_hint")
            )

            # 检查是否所有必需字段都已提取
            missing_fields = [
                field for field in required_fields if not extracted_data.get(field)
            ]

            if missing_fields:
                # 生成追问
                follow_up = await self._generate_follow_up(missing_fields)
                state.messages.append({"role": "assistant", "content": follow_up})

                # 保存已提取的数据
                state.user_data.update({k: v for k, v in extracted_data.items() if v})

                return None  # 保持当前状态
            else:
                # 所有字段都已提供，保存数据
                state.user_data.update(extracted_data)

        return next_state

    async def _process_file(
        self, state: SimpleChatState, message: str, config: Dict[str, Any]
    ) -> Optional[str]:
        """处理文件输入"""
        # 这里可以添加文件处理逻辑
        return config.get("next")

    async def _extract_fields(
        self, text: str, required_fields: List[str], prompt_hint: str = None
    ) -> Dict[str, Any]:
        """从文本中提取字段"""
        if not prompt_hint:
            return self._fallback_extraction(text, required_fields)

        prompt = f"""
        {prompt_hint}
        
        用户输入: {text}
        必需字段: {", ".join(required_fields)}
        
        请以JSON格式返回提取的字段。
        """

        response = await self.llm.ainvoke(
            [SystemMessage(content=prompt), HumanMessage(content=text)]
        )

        try:
            # 尝试解析JSON响应
            json_match = re.search(r"\{.*\}", response.content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        return self._fallback_extraction(text, required_fields)

    def _fallback_extraction(
        self, text: str, required_fields: List[str]
    ) -> Dict[str, Any]:
        """备用提取方法"""
        data = {}

        # 提取邮箱
        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        email_match = re.search(email_pattern, text)
        if email_match:
            data["email"] = email_match.group()

        # 提取手机号
        phone_pattern = r"1[3-9]\d{9}"
        phone_match = re.search(phone_pattern, text)
        if phone_match:
            data["phone"] = phone_match.group()

        # 提取姓名（简单模式）
        if "name" in required_fields:
            name_patterns = [r"我叫([^，。\s]+)", r"姓名[：:]\s*([^，。\s]+)"]
            for pattern in name_patterns:
                name_match = re.search(pattern, text)
                if name_match and len(name_match.group(1)) >= 2:
                    data["name"] = name_match.group(1)
                    break

        return data

    async def _generate_follow_up(self, missing_fields: List[str]) -> str:
        """生成追问问题"""
        prompt = f"""
        用户正在提供信息，但缺少以下字段: {", ".join(missing_fields)}
        
        请生成一个自然友好的追问，引导用户补充缺失信息。
        要求:
        1. 语气友好
        2. 明确指出需要什么信息
        3. 可以给出示例
        """

        response = await self.llm.ainvoke(
            [
                SystemMessage(content=prompt),
                HumanMessage(content=f"缺失字段: {missing_fields}"),
            ]
        )

        return response.content.strip()


class SimpleChatManager:
    """简化的聊天管理器"""

    def __init__(self, config_path: str):
        self.flow = SimpleStateChatFlow(config_path)
        self.sessions: Dict[str, SimpleChatState] = {}

    async def handle_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        # 获取或创建会话
        if session_id not in self.sessions:
            self.sessions[session_id] = SimpleChatState(
                current_state=self.flow.config["initial_state"]
            )

            # 添加欢迎消息
            initial_config = self.flow.states[self.flow.config["initial_state"]]
            self.sessions[session_id].messages.append(
                {"role": "assistant", "content": initial_config["prompt"]}
            )

        state = self.sessions[session_id]

        # 处理消息
        new_state = await self.flow.process_message(state, message)
        self.sessions[session_id] = new_state

        # 返回最新响应
        latest_message = new_state.messages[-1] if new_state.messages else {}

        return {
            "response": latest_message.get("content", ""),
            "current_state": new_state.current_state,
            "user_data": new_state.user_data,
            "is_complete": new_state.is_complete,
            "messages": new_state.messages,
        }

    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        if session_id in self.sessions:
            state = self.sessions[session_id]
            return {
                "current_state": state.current_state,
                "user_data": state.user_data,
                "message_count": len(state.messages),
                "is_complete": state.is_complete,
            }
        return {}

    def reset_session(self, session_id: str):
        """重置会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]


async def test_simple_flow():
    """测试简化的流程"""
    print("=== 测试简化的智能客服流程 ===\n")

    # 初始化管理器
    manager = SimpleChatManager("config/flow_config/simple_test_flow.yaml")

    session_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # 模拟对话流程
    test_messages = [
        "你好",
        "我还没有注册",
        "个人",
        "我叫李四，我的邮箱是****************，手机是13900139000",
        "我想办理服务A",
    ]

    for i, message in enumerate(test_messages, 1):
        print(f"--- 步骤 {i} ---")
        print(f"用户: {message}")

        result = await manager.handle_message(session_id, message)
        print(f"AI: {result['response']}")
        print(f"当前状态: {result['current_state']}")
        print(f"用户数据: {result['user_data']}")
        print(f"是否完成: {result['is_complete']}")
        print("\n" + "=" * 50 + "\n")

        # 短暂延迟
        await asyncio.sleep(0.5)

    # 获取会话总结
    session_info = manager.get_session_info(session_id)
    print("=== 会话总结 ===")
    print(f"会话ID: {session_id}")
    print(f"当前状态: {session_info.get('current_state')}")
    print(f"用户数据: {session_info.get('user_data')}")
    print(f"消息数量: {session_info.get('message_count')}")


if __name__ == "__main__":
    asyncio.run(test_simple_flow())
