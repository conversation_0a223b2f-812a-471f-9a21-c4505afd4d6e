"""
创建文件记录表的数据库迁移脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from storage.database import db_manager
from storage.models import Base, FileRecordTable
from loguru import logger


async def create_file_records_table():
    """创建文件记录表"""
    # 初始化数据库管理器
    await db_manager.init()

    async with db_manager.engine.begin() as conn:
        # 检查表是否已存在
        result = await conn.execute(
            text(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='file_records'"
            )
        )
        table_exists = result.fetchone() is not None

        if table_exists:
            logger.info("文件记录表已存在，跳过创建")
            return

        # 创建表
        await conn.execute(
            text("""
            CREATE TABLE file_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id VARCHAR(255) NOT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INTEGER NOT NULL,
                content_type VARCHAR(100) NOT NULL,
                upload_time DATETIME NOT NULL,
                status VARCHAR(20) DEFAULT 'active' NOT NULL,
                file_metadata TEXT DEFAULT '{}',
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL
            )
        """)
        )

        # 创建索引
        await conn.execute(
            text("CREATE INDEX idx_file_records_session_id ON file_records(session_id)")
        )
        await conn.execute(
            text("CREATE INDEX idx_file_records_filename ON file_records(filename)")
        )
        await conn.execute(
            text("CREATE INDEX idx_file_records_status ON file_records(status)")
        )

        logger.info("文件记录表创建成功")


if __name__ == "__main__":
    asyncio.run(create_file_records_table())
