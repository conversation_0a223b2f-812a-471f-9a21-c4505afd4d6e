# 数据库操作重构总结

## 重构目标
将 `api/endpoints.py` 中的大量数据库操作抽取到专门的服务模块中，提高代码的可维护性和可读性。

## 重构内容

### 1. 创建服务模块

#### 文件管理服务 (`services/file_service.py`)
- **FileService 类**：处理所有文件相关的数据库操作
  - `create_file_record()` - 创建文件记录
  - `get_file_records()` - 获取文件记录列表
  - `get_file_record()` - 获取单个文件记录
  - `update_file_status()` - 更新文件状态
  - `update_file_status_by_id()` - 通过ID更新文件状态
  - `create_file_upload_error()` - 创建文件上传错误记录
  - `get_file_list_response()` - 获取文件列表响应
  - `check_file_exists()` - 检查文件是否存在
  - `cleanup_orphaned_records()` - 清理孤立记录

#### 会话管理服务 (`services/session_service.py`)
- **SessionService 类**：处理会话相关的数据库操作
  - `create_session()` - 创建会话
  - `get_session()` - 获取会话
  - `list_sessions()` - 获取会话列表
  - `update_session_status()` - 更新会话状态
  - `delete_session()` - 删除会话
  - `pause_session()` - 暂停会话
  - `resume_session()` - 恢复会话

### 2. 更新 API 端点 (`api/endpoints.py`)

#### 导入语句更新
```python
# 从直接导入数据库模型
from storage.models import FileUploadErrorTable, FileRecordTable
from sqlalchemy import select, text
from storage.session_store import SessionStore

# 改为导入服务模块
from services.file_service import FileService
from services.session_service import SessionService
```

#### 文件上传接口重构
**之前**：
```python
# 创建文件记录
file_record = FileRecordTable(
    session_id=session_id,
    filename=file.filename,
    # ... 其他字段
)
db_session.add(file_record)
await db_session.commit()
```

**之后**：
```python
# 创建文件记录
await FileService.create_file_record(
    db_session=db_session,
    session_id=session_id,
    filename=file.filename,
    # ... 其他参数
)
```

#### 文件列表接口重构
**之前**：
```python
# 构建查询条件
if status == "all":
    query = select(FileRecordTable).where(FileRecordTable.session_id == session_id)
else:
    query = select(FileRecordTable).where(
        FileRecordTable.session_id == session_id,
        FileRecordTable.status == status
    )

result = await db_session.execute(query)
file_records = result.scalars().all()

# 计算总文件大小
total_size = sum(record.file_size for record in file_records)

# 转换为响应格式
files = [
    FileRecordResponse(
        # ... 字段映射
    )
    for record in file_records
]

return FileListResponse(
    files=files,
    total=len(files),
    total_size=total_size
)
```

**之后**：
```python
# 直接调用服务方法
return await FileService.get_file_list_response(db_session, session_id, status)
```

#### 文件下载接口重构
**之前**：
```python
# 查询文件记录
result = await db_session.execute(
    select(FileRecordTable).where(
        FileRecordTable.session_id == session_id,
        FileRecordTable.filename == filename,
        FileRecordTable.status == "active"
    )
)
file_record = result.scalar_one_or_none()

# 检查文件是否存在
if not os.path.exists(file_record.file_path):
    # 如果物理文件不存在，更新记录状态
    await db_session.execute(
        text("UPDATE file_records SET status = 'deleted', updated_at = :updated_at WHERE id = :id"),
        {"id": file_record.id, "updated_at": datetime.now()}
    )
    await db_session.commit()
    raise HTTPException(status_code=404, detail=f"文件 {filename} 已被删除")
```

**之后**：
```python
# 查询文件记录
file_record = await FileService.get_file_record(
    db_session=db_session,
    session_id=session_id,
    filename=filename,
    status="active"
)

if not file_record:
    raise HTTPException(status_code=404, detail=f"文件 {filename} 未找到或已被删除")

# 检查文件是否存在
if not os.path.exists(file_record.file_path):
    # 如果物理文件不存在，更新记录状态
    await FileService.update_file_status_by_id(
        db_session=db_session,
        record_id=file_record.id,
        status="deleted"
    )
    raise HTTPException(status_code=404, detail=f"文件 {filename} 已被删除")
```

#### 错误处理重构
**之前**：
```python
except Exception as e:
    logger.exception(f"文件上传失败: {e}")
    db_session = state_machine.db_session
    error_record = FileUploadErrorTable(
        session_id=session_id,
        filename=file.filename if file else "未知文件",
        error_message=str(e),
        upload_time=datetime.now(),
    )
    db_session.add(error_record)
    await db_session.commit()
    raise HTTPException(status_code=500, detail=f"文件上传失败: {e}")
```

**之后**：
```python
except Exception as e:
    logger.exception(f"文件上传失败: {e}")
    await FileService.create_file_upload_error(
        db_session=db_session,
        session_id=session_id,
        filename=file.filename if file else "未知文件",
        error_message=str(e),
        upload_time=datetime.now()
    )
    raise HTTPException(status_code=500, detail=f"文件上传失败: {e}")
```

## 重构效果

### 1. 代码组织
- **职责分离**：API端点专注于HTTP请求处理，业务逻辑放在服务层
- **代码复用**：相同的数据库操作逻辑可以在多个地方复用
- **可维护性**：数据库操作逻辑集中管理，便于维护和修改

### 2. 错误处理
- **统一错误处理**：服务层提供统一的错误处理和事务管理
- **事务安全**：所有数据库操作都有适当的事务处理和回滚机制

### 3. 可读性
- **简洁的API层**：API端点代码更加简洁，专注于HTTP层面的逻辑
- **清晰的业务逻辑**：服务层的代码结构清晰，易于理解和测试

### 4. 扩展性
- **易于扩展**：新的数据库操作可以直接添加到服务层
- **模块化设计**：服务模块可以独立测试和维护

## 代码质量提升

### 1. 遵循单一职责原则
- API端点：处理HTTP请求/响应
- 服务层：处理业务逻辑和数据库操作

### 2. 减少代码重复
- 相同的数据库操作逻辑被抽取到服务方法中
- 错误处理逻辑得到统一

### 3. 提高测试覆盖
- 服务层的方法可以独立进行单元测试
- 业务逻辑与HTTP层解耦，便于测试

## 未来改进建议

1. **添加更多服务模块**：可以继续抽取其他业务逻辑到专门的服务模块
2. **添加缓存层**：在服务层添加缓存机制提高性能
3. **添加日志记录**：在服务层添加更详细的操作日志
4. **添加数据验证**：在服务层添加数据验证逻辑

## 总结

通过这次重构，我们成功地：
- 将数据库操作从API层分离到服务层
- 提高了代码的可维护性和可读性
- 统一了错误处理和事务管理
- 为未来的功能扩展打下了良好的基础

这次重构遵循了良好的软件设计原则，使代码结构更加清晰，职责分离更加明确，为项目的长期维护和扩展提供了良好的基础。
