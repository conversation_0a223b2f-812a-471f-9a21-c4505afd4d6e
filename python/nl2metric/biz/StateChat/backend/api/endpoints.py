"""
API端点定义
"""

import os
import time
from datetime import datetime

from fastapi import APIRouter, HTTPException, UploadFile, File, Body, Depends, Query
from fastapi.responses import StreamingResponse, FileResponse
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from core.exceptions import FlowConfigError
from core.flow_manager import flow_manager
from core.intent import recognize_intent
from core.state_machine import StateMachine
from models.context import MessageRole, FlowContext
from models.state import InputType, UserInput, StateNode
from services.file_service import FileService
from storage import SessionStore
from storage.database import get_db_session
from utils import flatten_str
from utils.ai_engine import default_ai_engine
from utils.config_loader import ConfigLoader
from utils.mermaid_generator import MermaidFlowGenerator
from .schemas import (
    CreateSessionRequest,
    CreateSessionResponse,
    SessionDetailResponse,
    FileRenameRequest,
    FileRenameResponse,
    ValidationResultResponse,
    SuccessResponse,
    PaginationParams,
    PaginatedResponse,
    HealthCheckResponse,
    IntentType,
    ChatRequest,
    MermaidDiagramRequest,
    MermaidDiagramResponse,
    FileUploadResponse,
    FileListResponse,
)

# 创建路由
router = APIRouter()

# 启动时间
START_TIME = time.time()


# region: 辅助函数和依赖项
async def get_state_machine_for_session(
    session_id: str, db_session: AsyncSession = Depends(get_db_session)
) -> StateMachine:
    """
    FastAPI依赖项，用于根据会话ID获取并初始化正确的状态机。
    """
    session_store = SessionStore(db_session)
    session = await session_store.get_session(session_id)
    if not session:
        raise HTTPException(status_code=500, detail=f"会话 {session_id} 未找到")

    try:
        flow_config = flow_manager.get_flow_by_name(session.flow_name)
        return StateMachine(flow_config, db_session=db_session)
    except FlowConfigError as e:
        logger.exception(f"无法为会话 {session_id} 加载流程 '{session.flow_name}': {e}")
        raise HTTPException(status_code=500, detail=f"无法加载会话对应的流程配置: {e}")


# endregion


@router.post("/sessions", response_model=CreateSessionResponse)
async def create_session(
    request: CreateSessionRequest, db_session: AsyncSession = Depends(get_db_session)
):
    """
    创建新会话。可以选择指定的业务流程。
    """
    try:
        # 如果未指定流程名称，则使用默认流程
        flow_name = request.flow_name or flow_manager.default_flow_name
        flow_config = flow_manager.get_flow_by_name(flow_name)

        state_machine = StateMachine(flow_config, db_session=db_session)
        session = await state_machine.create_session(request.user_id)
        state = await state_machine.get_current_state(session.session_id)
        context = await state_machine.get_context(session.session_id)
        context.add_chat_message(MessageRole.ASSISTANT, flow_config.description)
        await state_machine.context_store.update_context(context)
        return CreateSessionResponse(
            session_id=session.session_id,
            flow_name=session.flow_name,
            current_state=session.current_state,
            prompt=state.prompt,
            input_type=state.input_type.value
            if hasattr(state.input_type, "value")
            else state.input_type,
            options=state.options,
            requires=state.requires,
        )

    except FlowConfigError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"创建会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# region: 意图处理辅助函数
async def _stream_chat_response(
    state_machine: StateMachine,
    context: FlowContext,
    text: str,
    intent: IntentType,
    current_state: StateNode,
):
    """为"闲聊"或"RAG查询"意图流式传输响应并更新上下文。"""
    context.add_chat_message(MessageRole.USER, text)

    if intent == IntentType.RAG_QUERY:
        # TODO: 在此实现RAG的特定逻辑，例如先从知识库检索信息
        logger.info("RAG意图的特定逻辑尚未实现，暂时作为普通聊天处理。")
        pass

    full_response_content = ""

    # 使用专门的闲聊模板
    async for chunk in default_ai_engine.get_chitchat_response_stream(
        text, current_state, context, state_machine.flow_config.description
    ):
        full_response_content += chunk
        yield chunk

    # 在流式响应结束后，将完整的助手回复添加到聊天历史并保存
    context.add_chat_message(MessageRole.ASSISTANT, full_response_content)
    await state_machine.context_store.update_context(context)


async def _stream_business_response(
    state_machine: StateMachine, session_id: str, req: ChatRequest
):
    """为"业务办理"意图处理状态机、流式传输响应并更新上下文。"""

    # 1. 首先获取当前状态和上下文
    current_state = await state_machine.get_current_state(session_id)
    context = await state_machine.get_context(session_id)

    # 2. 检查是否需要文件上传，如果需要且文件不齐全，则提示用户上传文件
    if current_state.input_type == InputType.FILE:
        file_upload_hint = await FileService.get_file_upload_hint(
            current_state, context
        )
        if file_upload_hint:
            # 如果需要文件上传但文件不齐全，直接返回上传提示，不处理状态转移
            context.add_chat_message(MessageRole.USER, req.text)

            # 使用LLM生成文件上传提示响应
            hint_template = ChatPromptTemplate.from_template(
                default_ai_engine.load_prompt_template("file_upload_hint_template"),
                template_format="jinja2",
            )
            chain = hint_template | default_ai_engine.chat_model | StrOutputParser()
            template_vars = {
                "user_input": req.text,
                "file_upload_hint": file_upload_hint,
                "current_state": flatten_str(current_state),
                "uploaded_files": [
                    file.model_dump()
                    for file in context.uploaded_files.get(current_state.name, [])
                ],
            }

            full_response_content = ""
            async for chunk in chain.astream(template_vars):
                full_response_content += chunk
                yield chunk

            # 将响应添加到聊天历史并保存
            context.add_chat_message(MessageRole.ASSISTANT, full_response_content)
            await state_machine.context_store.update_context(context)
            return

    # 3. 通过状态机处理用户输入
    user_input = UserInput(input_type=InputType.TEXT, value=req.text)
    result = await state_machine.process_user_input(session_id, user_input)

    # 4. 获取处理后的最新状态和上下文
    current_state = await state_machine.get_current_state(session_id)
    context = await state_machine.get_context(session_id)
    context.add_chat_message(MessageRole.USER, req.text)

    # 5. 检查是否需要文件上传，生成提示信息（将集成到模板中）
    file_upload_hint = await FileService.get_file_upload_hint(current_state, context)

    # 6. 使用LLM模板生成更自然的业务回复
    business_template = ChatPromptTemplate.from_template(
        default_ai_engine.load_prompt_template("business_response_template"),
        template_format="jinja2",
    )
    chain = business_template | default_ai_engine.chat_model | StrOutputParser()
    template_vars = {
        "user_input": req.text,
        "success": result.success,
        "error": result.error,
        "completed": result.completed,
        "processed_state": flatten_str(result.processed_state),
        "current_state": flatten_str(current_state),
        "context": flatten_str(context),
        "file_upload_hint": file_upload_hint,
        "flow_description": state_machine.flow_config.description,
        "followup_message": result.message,
        "progress": result.progress,
    }

    full_response_content = ""
    async for chunk in chain.astream(template_vars):
        full_response_content += chunk
        yield chunk

    # 7. 在流式响应结束后，将完整的助手回复添加到聊天历史并保存
    context.add_chat_message(MessageRole.ASSISTANT, full_response_content)
    await state_machine.context_store.update_context(context)


async def _stream_unknown_intent_response():
    """为未知意图流式传输一个标准的回退响应。"""
    yield "Sorry, I didn't understand your intention. Please provide more specific information."


# endregion


@router.post("/v1/process_message")
async def process_message(
    req: ChatRequest,
    db_session: AsyncSession = Depends(get_db_session),
):
    """统一处理用户查询，包括闲聊、知识库和业务办理"""
    if not req.session_id:
        raise HTTPException(status_code=400, detail="session_id 不能为空")

    try:
        state_machine = await get_state_machine_for_session(req.session_id, db_session)
        current_state: StateNode = await state_machine.get_current_state(req.session_id)
        context: FlowContext = await state_machine.get_context(req.session_id)
        intent: IntentType = await recognize_intent(req.text, context, current_state)

        if intent in (IntentType.CHAT, IntentType.RAG_QUERY):
            log_msg = "闲聊" if intent == IntentType.CHAT else "知识库查询"
            logger.info(f"会话 {req.session_id}: 意图识别为{log_msg}")
            return StreamingResponse(
                _stream_chat_response(
                    state_machine, context, req.text, intent, current_state
                )
            )

        elif intent == IntentType.BUSINESS:
            logger.info(f"会话 {req.session_id}: 意图识别为业务办理")
            return StreamingResponse(
                _stream_business_response(state_machine, req.session_id, req)
            )

        else:
            logger.warning(f"会话 {req.session_id}: 意图识别为未知")
            return StreamingResponse(_stream_unknown_intent_response())

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"处理查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/{session_id}", response_model=SessionDetailResponse)
async def get_session_detail(
    session_id: str,
    state_machine: StateMachine = Depends(get_state_machine_for_session),
):
    """获取会话详情"""
    try:
        session = await state_machine.get_session(session_id)
        context = await state_machine.get_context(session_id)
        current_state = await state_machine.get_current_state(session_id)

        return SessionDetailResponse(
            session_id=session.session_id,
            user_id=session.user_id,
            flow_name=session.flow_name,
            current_state=session.current_state,
            status=session.status,
            context=context.model_dump(),
            history=session.get_history(),
            state=current_state,
            created_at=session.created_at,
            updated_at=session.updated_at,
        )
    except Exception as e:
        logger.exception(f"获取会话详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions", response_model=PaginatedResponse)
async def list_sessions(
    params: PaginationParams = Depends(),
    db_session: AsyncSession = Depends(get_db_session),
):
    """获取会话列表"""
    try:
        # 注意：此处的list_sessions是SessionStore的方法，它不依赖于特定的状态机实例
        session_store = SessionStore(db_session)
        summaries = await session_store.list_sessions()

        start = (params.page - 1) * params.size
        end = start + params.size
        page_items = summaries[start:end]

        return PaginatedResponse.create(
            items=page_items, total=len(summaries), page=params.page, size=params.size
        )
    except Exception as e:
        logger.exception(f"获取会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sessions/{session_id}/pause")
async def pause_session(
    session_id: str,
    state_machine: StateMachine = Depends(get_state_machine_for_session),
):
    """暂停会话"""
    try:
        await state_machine.pause_session(session_id)
        return SuccessResponse(message=f"会话 {session_id} 已暂停")
    except Exception as e:
        logger.exception(f"暂停会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sessions/{session_id}/resume")
async def resume_session(
    session_id: str,
    state_machine: StateMachine = Depends(get_state_machine_for_session),
):
    """恢复会话"""
    try:
        await state_machine.resume_session(session_id)
        return SuccessResponse(message=f"会话 {session_id} 已恢复")
    except Exception as e:
        logger.exception(f"恢复会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    state_machine: StateMachine = Depends(get_state_machine_for_session),
):
    """删除会话"""
    try:
        await state_machine.delete_session(session_id)
        return SuccessResponse(message=f"会话 {session_id} 已删除")
    except Exception as e:
        logger.exception(f"删除会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sessions/{session_id}/files", response_model=FileUploadResponse)
async def upload_file(
    session_id: str,
    file: UploadFile = File(...),
    state_machine: StateMachine = Depends(get_state_machine_for_session),
    db_session: AsyncSession = Depends(get_db_session),
):
    """上传文件并自动验证友好回复"""
    try:
        current_state = await state_machine.get_current_state(session_id)
        # 调用服务层进行文件上传
        result = await FileService.upload_file(
            db_session=db_session,
            session_id=session_id,
            state_machine=state_machine,
            file=file,
            current_state=current_state,
        )
        # 更新上下文
        context = await state_machine.get_context(session_id)
        await state_machine.context_store.update_context(context)
        return FileUploadResponse(
            filename=result["filename"],
            file_path=result["file_path"],
            file_size=result["file_size"],
            content_type=result["content_type"],
            upload_time=result["upload_time"],
            file_id=result["file_id"],
            file_upload_hint=result.get("file_upload_hint"),
            validation_result=result.get("validation_result"),
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"文件上传失败: {e}")
        await FileService.create_file_upload_error(
            db_session=db_session,
            session_id=session_id,
            filename=file.filename if file else "未知文件",
            error_message=str(e),
            upload_time=datetime.now(),
        )
        raise HTTPException(status_code=500, detail=f"文件上传失败: {e}")


@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查"""
    default_flow = flow_manager.get_default_flow()
    return HealthCheckResponse(
        status="healthy", version=default_flow.version, uptime=time.time() - START_TIME
    )


@router.get("/api/flows")
async def get_flows():
    """获取可用流程列表"""
    flows = []
    for cfg in flow_manager.list_flows():
        flows.append(
            {
                "flow_name": cfg.flow_name,
                "description": getattr(cfg, "description", ""),
                "version": getattr(cfg, "version", ""),
                "initial_state": getattr(cfg, "initial_state", "welcome_user"),
                "final_states": getattr(cfg, "final_states", ["completed", "rejected"]),
                "total_states": len(getattr(cfg, "states", [])),
            }
        )
    return {"flows": flows}


@router.post("/validate-config", response_model=ValidationResultResponse)
async def validate_config(config_path: str = Body(..., embed=True)):
    """验证流程配置"""
    try:
        result = ConfigLoader.validate_config_file(config_path)
        return ValidationResultResponse(
            valid=result["valid"], errors=result["errors"], warnings=result["warnings"]
        )
    except Exception as e:
        logger.exception(f"配置验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/flows/mermaid", response_model=MermaidDiagramResponse)
async def generate_flow_mermaid(request: MermaidDiagramRequest):
    """
    根据流程配置动态生成Mermaid流程图

    支持的图表类型:
    - flowchart: 流程图 (默认)
    - stateDiagram: 状态图

    支持的样式主题:
    - default: 默认样式
    - colorful: 彩色主题
    - minimal: 简约主题
    """
    try:
        # 获取流程配置
        if request.flow_name:
            try:
                flow_config = flow_manager.get_flow_by_name(request.flow_name)
            except Exception as e:
                raise HTTPException(
                    status_code=404, detail=f"流程 '{request.flow_name}' 未找到: {e}"
                )
        else:
            flow_config = flow_manager.get_default_flow()

        # 创建Mermaid生成器
        generator = MermaidFlowGenerator(flow_config)

        # 生成图表代码
        if request.diagram_type == "stateDiagram":
            mermaid_code = generator.generate_state_diagram()
        else:  # flowchart (default)
            mermaid_code = generator.generate_flowchart(
                include_details=request.include_details, style_theme=request.style_theme
            )

        # 获取统计信息
        statistics = generator.get_flow_statistics()

        return MermaidDiagramResponse(
            flow_name=flow_config.flow_name,
            diagram_type=request.diagram_type,
            mermaid_code=mermaid_code,
            statistics=statistics,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"生成Mermaid图表失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成图表失败: {str(e)}")


@router.get("/api/flows/{flow_name}/mermaid")
async def get_flow_mermaid_simple(
    flow_name: str,
    diagram_type: str = Query("flowchart", description="图表类型"),
    include_details: str = Query("true", description="是否包含详细信息 (true/false)"),
    style_theme: str = Query("default", description="样式主题"),
):
    """
    获取指定流程的Mermaid图表 (GET方式，便于浏览器直接访问)
    """
    # 转换字符串布尔值
    include_details_bool = include_details.lower() in ("true", "1", "yes", "on")

    request = MermaidDiagramRequest(
        flow_name=flow_name,
        diagram_type=diagram_type,
        include_details=include_details_bool,
        style_theme=style_theme,
    )
    return await generate_flow_mermaid(request)


# region: 文件管理相关端点
@router.put("/sessions/{session_id}/files/rename", response_model=FileRenameResponse)
async def rename_uploaded_file(
    session_id: str,
    request: FileRenameRequest,
    state_machine: StateMachine = Depends(get_state_machine_for_session),
    db_session: AsyncSession = Depends(get_db_session),
):
    """重命名已上传的文件"""
    try:
        # 通过file_id查找文件记录
        file_record = await FileService.get_file_record_by_id(
            db_session=db_session, file_id=request.file_id
        )

        if not file_record:
            raise HTTPException(status_code=404, detail=f"文件记录未找到")

        # 调用服务层进行文件重命名
        result = await FileService.rename_file(
            db_session=db_session,
            session_id=session_id,
            file_id=request.file_id,
            new_filename=request.new_filename,
            document_type=request.document_type,
        )

        # 更新上下文
        context = await state_machine.get_context(session_id)
        await state_machine.context_store.update_context(context)

        return FileRenameResponse(
            success=True,
            old_filename=result["old_filename"],
            new_filename=result["new_filename"],
            message=result["message"],
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"文件重命名失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件重命名失败: {str(e)}")


@router.delete("/sessions/{session_id}/files/{file_id}")
async def delete_uploaded_file(
    session_id: str,
    file_id: int,
    state_machine: StateMachine = Depends(get_state_machine_for_session),
    db_session: AsyncSession = Depends(get_db_session),
):
    """删除已上传的文件"""
    try:
        # 调用服务层进行文件删除
        result = await FileService.delete_file(
            db_session=db_session,
            session_id=session_id,
            file_id=file_id,
            state_machine=state_machine,
        )

        return SuccessResponse(
            success=True,
            message=result["message"],
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"删除文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")


@router.get("/sessions/{session_id}/files", response_model=FileListResponse)
async def list_session_files(
    session_id: str,
    status: str = Query("active", description="文件状态过滤: active, deleted, all"),
    db_session: AsyncSession = Depends(get_db_session),
):
    """获取会话的文件列表"""
    try:
        return await FileService.get_file_list_response(db_session, session_id, status)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"获取文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")


@router.get("/sessions/{session_id}/files/{file_id}")
async def download_file(
    session_id: str,
    file_id: int,
    db_session: AsyncSession = Depends(get_db_session),
):
    """下载文件"""
    try:
        # 查询文件记录
        file_record = await FileService.get_file_record_by_id(
            db_session=db_session, file_id=file_id, status="active"
        )

        if not file_record:
            raise HTTPException(status_code=404, detail=f"文件记录未找到或已被删除")

        # 检查文件是否存在
        if not os.path.exists(file_record.file_path):
            # 如果物理文件不存在，更新记录状态
            await FileService.update_file_status_by_id(
                db_session=db_session, record_id=file_record.id, status="deleted"
            )
            raise HTTPException(status_code=404, detail=f"文件已被删除")

        # 返回文件
        return FileResponse(
            path=file_record.file_path,
            filename=file_record.original_filename,
            media_type=file_record.content_type,
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"文件下载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")
