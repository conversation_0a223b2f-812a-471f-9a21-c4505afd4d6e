"""
API数据模型定义
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field

from models.session import SessionStatus
from models.state import InputType, StateNode
from models.file_validation import (
    FileValidationSummary,
    ValidationStatus,
    ValidationType,
)


class CreateSessionRequest(BaseModel):
    """创建会话请求"""

    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: Optional[str] = Field(
        None, description="要启动的流程名称，如果未提供，则使用默认流程"
    )


class ChatRequest(BaseModel):
    """聊天请求"""

    text: str = Field(..., description="用户输入文本")
    session_id: Optional[str] = Field(None, description="会话ID")


class CreateSessionResponse(BaseModel):
    """创建会话响应"""

    session_id: str = Field(..., description="会话ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    prompt: str = Field(..., description="状态提示")
    input_type: str = Field(..., description="输入类型")
    options: Optional[List[str]] = Field(None, description="选择项")
    requires: Optional[List[str]] = Field(None, description="必需字段")


class UserInputRequest(BaseModel):
    """用户输入请求"""

    input_type: InputType = Field(..., description="输入类型")
    value: Union[str, List[str], Dict[str, Any]] = Field(..., description="输入值")
    files: Optional[List[str]] = Field(None, description="上传文件路径列表")


class ProcessInputResponse(BaseModel):
    """处理输入响应"""

    success: bool = Field(..., description="当前是否成功")
    partial_success: bool = Field(
        False, description="是否部分成功（例如参数部分提取成功）"
    )
    completed: bool = Field(False, description="所有流程是否完成")
    error: Optional[str] = Field(None, description="错误信息")
    intent_type: Optional[str] = Field(None, description="意图类型")
    processed_state: Optional[StateNode] = Field(
        None, description="处理用户输入前的状态"
    )
    current_state: StateNode = Field(None, description="处理用户输入后的当前状态")
    next_states: List[StateNode] = Field(
        default_factory=list, description="下一个状态集"
    )
    progress: Optional[float] = Field(None, description="进度百分比")
    message: Optional[str] = Field(None, description="消息")
    missing_fields: List[str] = Field(
        default_factory=list, description="缺失的字段列表"
    )
    followup_required: bool = Field(False, description="是否需要用户进一步输入")


class SessionSummaryResponse(BaseModel):
    """会话摘要响应"""

    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    status: SessionStatus = Field(..., description="会话状态")
    progress_percentage: float = Field(..., description="进度百分比")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class SessionDetailResponse(BaseModel):
    """会话详情响应"""

    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    status: SessionStatus = Field(..., description="会话状态")
    context: Dict[str, Any] = Field(..., description="会话上下文")
    history: List[Dict[str, Any]] = Field(..., description="状态转移历史")
    state: StateNode = Field(..., description="当前状态节点")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class IntentProcessResponse(BaseModel):
    """意图处理响应"""

    intent_type: str = Field(..., description="意图类型")
    success: bool = Field(..., description="是否成功")
    response: Optional[str] = Field(None, description="处理响应")
    should_continue_flow: bool = Field(..., description="是否继续业务流程")
    data: Optional[Dict[str, Any]] = Field(None, description="返回数据")


class FileUploadResponse(BaseModel):
    """文件上传响应"""

    filename: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件存储路径")
    file_size: int = Field(..., description="文件大小")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(..., description="上传时间")
    file_id: int = Field(..., description="文件记录ID")
    validation_result: Optional[FileValidationSummary] = Field(
        None, description="文件验证结果"
    )
    file_upload_hint: Optional[str] = Field(None, description="文件上传提示")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileRecordResponse(BaseModel):
    """文件记录响应"""

    id: int = Field(..., description="记录ID")
    session_id: str = Field(..., description="会话ID")
    filename: str = Field(..., description="当前文件名")
    original_filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="文件存储路径")
    file_size: int = Field(..., description="文件大小(字节)")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(..., description="上传时间")
    status: str = Field(..., description="文件状态")
    file_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="文件元数据"
    )
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileListResponse(BaseModel):
    """文件列表响应"""

    files: List[FileRecordResponse] = Field(..., description="文件记录列表")
    total: int = Field(..., description="总文件数")
    total_size: int = Field(..., description="总文件大小(字节)")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileDownloadRequest(BaseModel):
    """文件下载请求"""

    session_id: str = Field(..., description="会话ID")
    file_id: int = Field(..., description="文件记录ID")


class FileValidationResponse(BaseModel):
    """文件验证响应"""

    session_id: str = Field(..., description="会话ID")
    state_name: str = Field(..., description="状态名称")
    validation_summary: FileValidationSummary = Field(..., description="验证摘要")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")
    can_proceed: bool = Field(..., description="是否可以继续流程")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileRenameRequest(BaseModel):
    """文件重命名请求"""

    file_id: int = Field(..., description="文件记录ID")
    new_filename: str = Field(..., description="新文件名")
    document_type: str = Field(..., description="文档类型")


class FileRenameResponse(BaseModel):
    """文件重命名响应"""

    success: bool = Field(..., description="是否成功")
    old_filename: str = Field(..., description="旧文件名")
    new_filename: str = Field(..., description="新文件名")
    message: str = Field(..., description="消息")


class IntentType(str, Enum):
    """意图类型"""

    CHAT = "chat"  # 闲聊
    RAG_QUERY = "rag_query"  # 查询知识库
    BUSINESS = "business"  # 继续业务办理
    UNKNOWN = "unknown"  # 未知意图


class IntentResponse(BaseModel):
    """意图响应"""

    intent_type: IntentType = Field(..., description="意图类型")


class FlowConfigResponse(BaseModel):
    """流程配置响应"""

    flow_name: str = Field(..., description="流程名称")
    description: str = Field(..., description="流程描述")
    version: str = Field(..., description="版本号")
    initial_state: str = Field(..., description="初始状态")
    final_states: List[str] = Field(..., description="终止状态列表")
    total_states: int = Field(..., description="总状态数")


class StateInfoResponse(BaseModel):
    """状态信息响应"""

    name: str = Field(..., description="状态名称")
    prompt: str = Field(..., description="状态提示")
    input_type: str = Field(..., description="输入类型")
    options: Optional[List[str]] = Field(None, description="选择项")
    requires: Optional[List[str]] = Field(None, description="必需字段")
    next_states: Optional[List[str]] = Field(None, description="可能的下一状态")


class ValidationResultResponse(BaseModel):
    """验证结果响应"""

    valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(..., description="错误列表")
    warnings: List[str] = Field(..., description="警告列表")


class ErrorResponse(BaseModel):
    """错误响应"""

    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class SuccessResponse(BaseModel):
    """成功响应"""

    success: bool = Field(True, description="操作成功")
    message: str = Field(..., description="成功消息")
    data: Optional[Dict[str, Any]] = Field(None, description="返回数据")


class PaginationParams(BaseModel):
    """分页参数"""

    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")


class PaginatedResponse(BaseModel):
    """分页响应"""

    items: List[Any] = Field(..., description="数据项列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")

    @classmethod
    def create(cls, items: List[Any], total: int, page: int, size: int):
        """创建分页响应"""
        pages = (total + size - 1) // size  # 向上取整
        return cls(items=items, total=total, page=page, size=size, pages=pages)


class HealthCheckResponse(BaseModel):
    """健康检查响应"""

    status: str = Field("healthy", description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="服务版本")
    uptime: Optional[float] = Field(None, description="运行时间(秒)")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class MermaidDiagramRequest(BaseModel):
    """Mermaid图表生成请求"""

    flow_name: Optional[str] = Field(None, description="流程名称，不提供则使用默认流程")
    diagram_type: str = Field(
        "flowchart", description="图表类型: flowchart, stateDiagram"
    )
    include_details: bool = Field(True, description="是否包含状态详细信息")
    style_theme: str = Field(
        "default", description="样式主题: default, colorful, minimal"
    )


class MermaidDiagramResponse(BaseModel):
    """Mermaid图表生成响应"""

    flow_name: str = Field(..., description="流程名称")
    diagram_type: str = Field(..., description="图表类型")
    mermaid_code: str = Field(..., description="Mermaid图表代码")
    statistics: Dict[str, Any] = Field(..., description="流程统计信息")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
