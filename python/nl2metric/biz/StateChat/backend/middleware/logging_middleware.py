import json
import time
import traceback
from typing import Callable, Awaitable

from fastapi import Request, Response
from fastapi.datastructures import FormData
from loguru import logger
from starlette.datastructures import UploadFile
from starlette.middleware.base import BaseHTTPMiddleware


class RequestResponseLoggingMiddleware(BaseHTTPMiddleware):
    """记录请求和响应体的中间件"""

    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        # 读取请求体
        request_body = None
        try:
            request_body = await request.body()
            if request_body:
                request_body = json.loads(request_body.decode("utf-8"))
        except Exception:
            pass

        # 处理文件上传的元数据
        if isinstance(request_body, FormData):
            processed_body = {}
            for key, value in request_body.items():
                if isinstance(value, UploadFile):
                    processed_body[key] = {
                        "filename": getattr(value, 'filename', None),
                        "content_type": getattr(value, 'content_type', None),
                        "size": getattr(value, 'size', None),
                    }
                else:
                    processed_body[key] = value
            request_body = processed_body

        # 记录请求信息
        logger.info(
            f"请求: {request.method} {request.url}\n"
            f"请求体: {request_body}" if not isinstance(request_body, bytes) else "请求体: <binary>"
        )

        try:
            response = await call_next(request)
            return response  # 直接返回原始流式响应
        except Exception as e:
            logger.error(
                f"请求处理异常: {request.method} {request.url}\n"
                f"异常信息: {str(e)}\n"
                f"堆栈跟踪: {traceback.format_exc()}"
            )
            raise