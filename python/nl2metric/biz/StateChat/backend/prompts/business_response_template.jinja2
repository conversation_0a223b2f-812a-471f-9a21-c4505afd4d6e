作为专业的服务助手，请根据以下信息生成 Markdown 格式的响应：
## 流程描述
{{flow_description}}

## 处理状态概览

- **刚处理的状态**: `{{processed_state}}`
- **执行结果**: {{'✅ 成功' if success else '❌ 失败'}}
{% if error %}
- **错误信息**: `{{error}}`
{% endif %}
- **流程完成度**:
{% if completed %}
🎉 全部完成
{% else %}
{{ (progress) | round(1) }}% 🔄 进行中
{% endif %}
- **当前状态**: `{{current_state}}`
- **追问消息**: `{{followup_message}}`

## 上下文信息

**已收集的数据**:
{{context}}

**missing_fields 缺失的字段**
pending_extraction 是否有必要的字段缺失，如果有，请列出缺失的字段。


**用户最新输入**:
> "{{user_input}}"

{% if file_upload_hint %}
## 文件上传提示
{{file_upload_hint}}
{% endif %}

## 响应生成要求

请按照以下 Markdown 结构生成响应：

### 📋 当前进度
简要说明当前进度和阶段流转状态，包含上一阶段和当前阶段

### 👤 用户确认
确认已理解用户输入的内容

### 🎯 下一步引导
清晰说明需要用户执行的操作
{% if file_upload_hint %}
- 包含文件上传的具体指导
{% endif %}

{% if not success %}
### ⚠️ 状态提示
针对遇到的问题提供解决方案，用一个温和、通用的短语来传达流程状态信息
{% endif %}

## 流程引导
1. 必须完全按照当前流程状态机配置输出选项
2. 额外指引信息应保留到对应专项阶段输出

## 特殊指导
1. **语气风格**: 保持专业、友好、鼓励的语气
2. **结构清晰**: 使用 Markdown 标题、列表、引用等格式
3. **重点突出**: 关键信息使用粗体或代码块标记
4. **文件上传**: 如有文件上传需求(input_type: "file")，提供具体的格式、大小、内容、文件名要求
5. **异常处理**: 针对不同类型错误提供具体解决建议
6. **进度可视化**: 使用表情符号或进度条展示完成度
7. **超链接**: 带链接的请保留原字段，并使用超链接Markdown格式
8. **英文格式**: 带"_"的英文格式化为多个单词，例如"register_acra"需要格式化为"register acra"
10. **响应生成**: 响应一定要基于当前状态生成，提示信息基于办理此流程给用户的提示信息和追问消息生成，必须用全英文回答

## 输出格式

请直接输出 Markdown 格式的响应，不要包含此模板中的任何说明文字，不要用```markdown ```包裹。
