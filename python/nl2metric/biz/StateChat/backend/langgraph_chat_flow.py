"""
基于LangGraph的智能客服聊天流程实现
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, TypedDict, Union

import yaml
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from pydantic import BaseModel


class ChatState(TypedDict):
    """聊天状态"""

    current_state: str
    messages: List[Union[HumanMessage, AIMessage, SystemMessage]]
    user_data: Dict[str, Any]
    collected_files: List[Dict[str, Any]]
    flow_config: Dict[str, Any]
    is_complete: bool


class FileInfo(BaseModel):
    """文件信息模型"""

    filename: str
    content_type: str
    size: int
    content: Optional[str] = None


class StateChatNode:
    """状态节点处理类"""

    def __init__(self, node_config: Dict[str, Any]):
        self.config = node_config
        self.name = node_config["name"]
        self.prompt = node_config["prompt"]
        self.input_type = node_config.get("input_type", "text")
        self.llm = ChatOpenAI(
            model="qwen3_32b",
            base_url="http://123.181.192.99:16701/v1",
            api_key="xxxx",
            temperature=0.1,
            streaming=False,
        )

    async def process_input(self, user_input: str, state: ChatState) -> Dict[str, Any]:
        """处理用户输入"""
        if self.input_type == "choice":
            return await self._process_choice_input(user_input, state)
        elif self.input_type == "text":
            return await self._process_text_input(user_input, state)
        elif self.input_type == "file":
            return await self._process_file_input(user_input, state)
        else:
            return {"next_state": None}

    async def _process_choice_input(
        self, user_input: str, state: ChatState
    ) -> Dict[str, Any]:
        """处理选择输入"""
        options = self.config.get("options", [])

        # 如果有LLM提示，使用LLM解析用户意图

        response = await self.llm.ainvoke(
            [
                SystemMessage(content=self.config["llm_prompt_hint"])
                if "llm_prompt_hint" in self.config
                else SystemMessage(content=""),
                HumanMessage(
                    content=f"用户输入: {user_input}\n可选选项: {', '.join(options)}"
                ),
            ]
        )
        choice = response.content.strip()

        # 尝试匹配选项
        for option in options:
            if option.lower() in choice.lower() or choice.lower() in option.lower():
                return {"next_state": self.config["next"].get(option, None)}

        # 直接匹配选项
        for option in options:
            if option.lower() in user_input.lower():
                return {"next_state": self.config["next"].get(option, None)}

        return {"next_state": None, "error": f"请选择有效选项: {', '.join(options)}"}

    async def _process_text_input(
        self, user_input: str, state: ChatState
    ) -> Dict[str, Any]:
        """处理文本输入"""
        result = {"next_state": self.config.get("next")}

        # 如果需要提取字段
        if "requires" in self.config:
            extracted_data = await self._extract_fields(
                user_input, self.config["requires"]
            )
            result["extracted_data"] = extracted_data

            # 检查是否所有必需字段都已提取
            missing_fields = [
                field
                for field in self.config["requires"]
                if not extracted_data.get(field)
            ]
            if missing_fields:
                result["error"] = f"请提供以下信息: {', '.join(missing_fields)}"
                result["next_state"] = None  # 保持当前状态

        return result

    async def _extract_fields(
        self, text: str, required_fields: List[str]
    ) -> Dict[str, Any]:
        """从文本中提取字段"""
        if "llm_prompt_hint" not in self.config:
            return {}

        prompt = f"""
        {self.config["llm_prompt_hint"]}
        
        用户输入: {text}
        必需字段: {", ".join(required_fields)}
        
        请以JSON格式返回提取的字段，字段名作为键，提取的值作为值。
        如果某个字段无法提取，使用null作为值。
        """

        response = await self.llm.ainvoke(
            [SystemMessage(content=prompt), HumanMessage(content=text)]
        )

        try:
            # 尝试解析JSON响应
            json_match = re.search(r"\{.*\}", response.content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        return {}

    async def _process_file_input(
        self, user_input: str, state: ChatState
    ) -> Dict[str, Any]:
        """处理文件输入"""
        result = {"next_state": self.config.get("next")}

        # 这里可以添加文件处理逻辑
        # 实际应用中，文件可能通过其他方式上传

        return result


class StateChatFlow:
    """基于LangGraph的智能客服流程"""

    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.nodes = {
            name: StateChatNode(node_config)
            for name, node_config in self.config["states"].items()
        }
        self.graph = self._build_graph()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载流程配置"""
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    def _build_graph(self) -> StateGraph:
        """构建状态图"""
        workflow = StateGraph(ChatState)

        # 添加所有节点
        for node_name in self.config["states"].keys():
            workflow.add_node(node_name, self._create_node_handler(node_name))

        # 设置入口点
        workflow.set_entry_point(self.config["initial_state"])

        # 添加边
        for node_name, node_config in self.config["states"].items():
            next_config = node_config.get("next")

            if next_config is None:
                # 终止状态
                workflow.add_edge(node_name, END)
            elif isinstance(next_config, str):
                # 单一 next 状态
                workflow.add_edge(node_name, next_config)
            elif isinstance(next_config, dict):
                # 条件转移
                workflow.add_conditional_edges(
                    node_name,
                    self._create_router(node_name, next_config),
                    {
                        choice: target_state or END
                        for choice, target_state in next_config.items()
                    },
                )

        return workflow.compile()

    def _create_node_handler(self, node_name: str):
        """创建节点处理器"""

        async def handler(state: ChatState) -> ChatState:
            node = self.nodes[node_name]

            # 添加系统提示
            system_msg = SystemMessage(content=node.prompt)
            state["messages"].append(system_msg)

            # 如果是用户交互节点，等待用户输入
            if node.input_type in ["text", "choice", "file"]:
                # 这里在实际应用中会等待用户输入
                # 为了演示，我们直接返回状态
                return state
            else:
                # 自动处理节点
                ai_response = AIMessage(content="处理中...")
                state["messages"].append(ai_response)

                # 自动转移到下一个状态
                next_state = node.config.get("next")
                if next_state:
                    state["current_state"] = next_state

                return state

        return handler

    def _create_router(self, node_name: str, next_config: Dict[str, str]):
        """创建路由器"""

        async def router(state: ChatState) -> str:
            # 获取最后一条用户消息
            user_messages = [
                msg for msg in state["messages"] if isinstance(msg, HumanMessage)
            ]
            if not user_messages:
                return list(next_config.values())[0]  # 默认第一个选项

            user_input = user_messages[-1].content
            node = self.nodes[node_name]

            # 处理输入并确定下一个状态
            result = await node.process_input(user_input, state)

            if "error" in result:
                # 如果有错误，保持在当前状态
                error_msg = AIMessage(content=result["error"])
                state["messages"].append(error_msg)
                return node_name

            if result.get("next_state"):
                # 保存提取的数据
                if "extracted_data" in result:
                    state["user_data"].update(result["extracted_data"])

                return result["next_state"]

            # 默认返回第一个选项
            return list(next_config.values())[0]

        return router

    async def process_message(
        self,
        message: str,
        current_state: str = None,
        user_data: Dict = None,
        files: List = None,
    ) -> Dict[str, Any]:
        """处理用户消息"""
        # 初始化状态
        state: ChatState = {
            "current_state": current_state or self.config["initial_state"],
            "messages": [],
            "user_data": user_data or {},
            "collected_files": files or [],
            "flow_config": self.config,
            "is_complete": False,
        }

        # 添加用户消息
        state["messages"].append(HumanMessage(content=message))

        # 运行图
        result = await self.graph.ainvoke(state)

        # 提取AI响应
        ai_messages = [
            msg for msg in result.get("messages", []) if isinstance(msg, AIMessage)
        ]

        return {
            "response": ai_messages[-1].content if ai_messages else "",
            "current_state": result.get("current_state"),
            "user_data": result.get("user_data", {}),
            "is_complete": result.get("is_complete", False),
        }

    def get_current_prompt(self, state_name: str) -> str:
        """获取当前状态的提示"""
        if state_name in self.nodes:
            return self.nodes[state_name].prompt
        return ""


class StateChatManager:
    """状态聊天管理器"""

    def __init__(self, config_path: str):
        self.flow = StateChatFlow(config_path)
        self.sessions: Dict[str, Dict[str, Any]] = {}

    async def handle_message(
        self, session_id: str, message: str, files: List[Dict] = None
    ) -> Dict[str, Any]:
        """处理用户消息"""
        # 获取或创建会话
        if session_id not in self.sessions:
            self.sessions[session_id] = {
                "current_state": None,
                "user_data": {},
                "files": files or [],
            }

        session = self.sessions[session_id]

        # 处理消息
        result = await self.flow.process_message(
            message=message,
            current_state=session["current_state"],
            user_data=session["user_data"],
            files=files or [],
        )

        # 更新会话状态
        session["current_state"] = result["current_state"]
        session["user_data"] = result["user_data"]

        # 检查是否完成
        if result["is_complete"]:
            # 可以在这里保存会话数据或执行其他清理操作
            pass

        return result

    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        return self.sessions.get(session_id, {})

    def reset_session(self, session_id: str):
        """重置会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]


# 示例使用
async def main():
    """示例：使用状态聊天流程"""
    # 初始化管理器
    manager = StateChatManager("config/flow_config/simple_test_flow.yaml")

    # 模拟对话
    session_id = "user_123"

    # 第一条消息
    result1 = await manager.handle_message(session_id, "你好")
    print(f"AI: {result1['response']}")
    print(f"当前状态: {result1['current_state']}")

    # 模拟用户选择
    result2 = await manager.handle_message(session_id, "否")
    print(f"AI: {result2['response']}")
    print(f"当前状态: {result2['current_state']}")

    # 模拟注册
    result3 = await manager.handle_message(session_id, "个人")
    print(f"AI: {result3['response']}")
    print(f"当前状态: {result3['current_state']}")

    # 提供个人信息
    result4 = await manager.handle_message(
        session_id, "我叫张三，电话是13800138000，邮箱是********************"
    )
    print(f"AI: {result4['response']}")
    print(f"收集的数据: {result4['user_data']}")
    print(f"当前状态: {result4['current_state']}")


if __name__ == "__main__":
    asyncio.run(main())
