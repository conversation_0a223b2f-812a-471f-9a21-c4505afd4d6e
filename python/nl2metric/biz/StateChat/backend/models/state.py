"""
状态节点模型定义
"""

import uuid
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field
from enum import Enum

from models.file_validation import FileValidationConfig


class InputType(str, Enum):
    """输入类型枚举"""

    TEXT = "text"
    CHOICE = "choice"
    FILE = "file"


class StateNode(BaseModel):
    """状态节点模型"""

    name: str = Field(..., description="流程名称")
    prompt: str = Field(..., description="办理此流程给用户的提示信息")
    input_type: InputType = Field(..., description="办理此流程输入类型[text, choice]")
    options: Optional[List[str]] = Field(None, description="办理此流程输入选择项列表")
    requires: Optional[List[str]] = Field(
        None, description="办理此流程输入必需的字段列表"
    )
    llm_prompt_hint: Optional[str] = Field(
        None, description="办理此流程给LLM调用的额外辅助提示语"
    )
    next: Optional[Union[str, Dict[str, str]]] = Field(None, description="下一个流程")
    file_validation: Optional[Union[FileValidationConfig, Dict[str, Any]]] = Field(
        None, description="文件验证配置"
    )

    class Config:
        use_enum_values = True


class FlowConfig(BaseModel):
    """流程配置模型"""

    flow_name: str = Field(..., description="流程名称")
    description: str = Field(..., description="流程描述")
    version: str = Field(..., description="版本号")
    states: Dict[str, StateNode] = Field(..., description="状态字典")
    initial_state: str = Field(..., description="初始状态")
    final_states: List[str] = Field(..., description="终止状态列表")


class StateTransition(BaseModel):
    """状态转移模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="转移ID")
    from_state: str = Field(..., description="源状态")
    to_state: str = Field(..., description="目标状态")
    condition: Optional[str] = Field(None, description="转移条件")
    user_input: Optional[dict] = Field(None, description="用户输入")


class UserInput(BaseModel):
    """用户输入模型"""

    input_type: InputType
    value: str  # value只支持文本输入
    timestamp: Optional[str] = Field(None, description="输入时间戳")
