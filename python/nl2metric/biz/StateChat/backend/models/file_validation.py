"""
文件验证配置和结果模型定义
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ValidationStatus(str, Enum):
    """验证状态枚举"""

    PENDING = "pending"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ValidationType(str, Enum):
    """验证类型枚举"""

    FILE_TYPE = "file_type"
    FILE_SIZE = "file_size"
    FILE_NAME = "file_name"
    REQUIRED_FILES = "required_files"
    FILE_INTEGRITY = "file_integrity"
    MULTIMODAL = "multimodal"


class FileValidationRule(BaseModel):
    """文件验证规则模型"""

    name_pattern: str = Field(..., description="文件名匹配模式")
    description: str = Field(..., description="文件描述")
    min_count: int = Field(1, description="最小文件数量")
    max_count: Optional[int] = Field(None, description="最大文件数量")


class NamingConvention(BaseModel):
    """文件命名规范模型"""

    pattern: str = Field(..., description="命名模式")
    description: str = Field(..., description="描述")


class MultimodalValidationConfig(BaseModel):
    """多模态验证配置模型"""

    enabled: bool = Field(False, description="是否启用多模态验证")
    confidence_threshold: float = Field(0.8, description="置信度阈值")
    content_types: List[str] = Field(default_factory=list, description="期望的内容类型")
    auto_validate: bool = Field(True, description="是否在上传后自动验证")


class FileValidationConfig(BaseModel):
    """文件验证配置模型"""

    validation_strategy: str = Field(
        "basic", description="验证策略: basic, strict, multimodal"
    )
    allowed_types: List[str] = Field(default_factory=list, description="允许的文件类型")
    max_size_mb: int = Field(10, description="最大文件大小(MB)")
    required_files: List[FileValidationRule] = Field(
        default_factory=list, description="必需文件规则"
    )
    naming_convention: Optional[NamingConvention] = Field(
        None, description="文件命名规范"
    )
    multimodal_config: Optional[MultimodalValidationConfig] = Field(
        None, description="多模态验证配置"
    )
    auto_validate: bool = Field(True, description="是否在上传后自动验证")


class ValidationResult(BaseModel):
    """验证结果模型"""

    validation_type: ValidationType = Field(..., description="验证类型")
    status: ValidationStatus = Field(..., description="验证状态")
    message: str = Field(..., description="验证消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="验证时间")


class FileValidationError(BaseModel):
    """文件验证错误模型"""

    filename: str = Field(..., description="文件名")
    error_type: ValidationType = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误消息")
    suggestion: Optional[str] = Field(None, description="建议")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")


class FileValidationSummary(BaseModel):
    """文件验证摘要模型"""

    session_id: str = Field(..., description="会话ID")
    state_name: str = Field(..., description="状态名称")
    total_files: int = Field(0, description="总文件数")
    valid_files: int = Field(0, description="有效文件数")
    invalid_files: int = Field(0, description="无效文件数")
    validation_results: List[ValidationResult] = Field(
        default_factory=list, description="验证结果"
    )
    errors: List[FileValidationError] = Field(
        default_factory=list, description="错误列表"
    )
    is_valid: bool = Field(False, description="是否全部有效")
    timestamp: datetime = Field(default_factory=datetime.now, description="验证时间")


class MultimodalValidationResult(BaseModel):
    """多模态验证结果模型"""

    filename: str = Field(..., description="文件名")
    content_type: str = Field(..., description="内容类型")
    confidence_score: float = Field(..., description="置信度分数")
    content_description: str = Field(..., description="内容描述")
    detected_objects: List[str] = Field(
        default_factory=list, description="检测到的对象"
    )
    extracted_text: Optional[str] = Field(None, description="提取的文本")
    is_valid_content: bool = Field(..., description="内容是否有效")
    validation_message: str = Field(..., description="验证消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="验证时间")
